"use client";

import { Marquee } from "@/components/magicui/marquee";
import { FaStar } from "react-icons/fa";

const reviews = [
  {
    name: "<PERSON><PERSON>",
    rating: 5,
    text: "AGKraft delivered my project on time,",
    highlightText: "flawless design",
    endText: "and full responsiveness. Loved it!",
    color: "text-green-400",
    highlightColor: "text-green-400",
    height: "h-52",
  },
  {
    name: "<PERSON><PERSON>",
    rating: 5,
    text: "Highly recommend for startups —",
    highlightText: "fast execution",
    endText: "with clean UI and smart delivery.",
    color: "text-pink-400",
    highlightColor: "text-pink-400",
    height: "h-48",
  },
  {
    name: "<PERSON><PERSON> Verma",
    rating: 5,
    text: "They simplified our business tech,",
    highlightText: "scalable results",
    endText: "and modern user experience.",
    color: "text-yellow-400",
    highlightColor: "text-yellow-400",
    height: "h-56",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    rating: 5,
    text: "From first call to delivery,",
    highlightText: "great communication",
    endText: "and amazing design sense.",
    color: "text-blue-400",
    highlightColor: "text-blue-400",
    height: "h-52",
  },
  {
    name: "Saurabh Yadav",
    rating: 5,
    text: "Understood our business goals,",
    highlightText: "sharp execution",
    endText: "and fast turnaround.",
    color: "text-blue-700",
    highlightColor: "text-blue-700",
    height: "h-44",
  },
  {
    name: "Ritika Mehta",
    rating: 5,
    text: "Very satisfied with their team,",
    highlightText: "SEO-friendly build",
    endText: "and responsive support.",
    color: "text-orange-400",
    highlightColor: "text-orange-400",
    height: "h-44",
  },
  {
    name: "Arvind Gupta",
    rating: 5,
    text: "AGKraft turned our idea into reality,",
    highlightText: "excellent quality",
    endText: "and beautiful visuals.",
    color: "text-purple-600",
    highlightColor: "text-purple-600",
    height: "h-48",
  },
  {
    name: "Deepika Rathi",
    rating: 5,
    text: "Professional team and quick updates,",
    highlightText: "mobile-first focus",
    endText: "with great UI.",
    color: "text-red-400",
    highlightColor: "text-red-400",
    height: "h-44",
  },
  {
    name: "Ankit Sinha",
    rating: 5,
    text: "They modernized our old site,",
    highlightText: "sleek and fast",
    endText: "with strong backend logic.",
    color: "text-teal-500",
    highlightColor: "text-teal-500",
    height: "h-48",
  },
  {
    name: "Megha Choudhary",
    rating: 5,
    text: "Everything we needed under one roof —",
    highlightText: "end-to-end service",
    endText: "and timely delivery.",
    color: "text-amber-500",
    highlightColor: "text-amber-500",
    height: "h-44",
  },
  {
    name: "Kunal Raj",
    rating: 5,
    text: "They resolved issues quickly with",
    highlightText: "great support",
    endText: "and proactive feedback.",
    color: "text-rose-500",
    highlightColor: "text-rose-500",
    height: "h-44",
  },
  {
    name: "Tanvi Bansal",
    rating: 5,
    text: "A very smooth experience with",
    highlightText: "creative minds",
    endText: "and strong delivery ethics.",
    color: "text-indigo-500",
    highlightColor: "text-indigo-500",
    height: "h-52",
  },
  {
    name: "Rohan Kapoor",
    rating: 5,
    text: "We saw growth fast thanks to",
    highlightText: "strong SEO",
    endText: "and UI that converts.",
    color: "text-fuchsia-500",
    highlightColor: "text-fuchsia-500",
    height: "h-48",
  },
  {
    name: "Isha Arora",
    rating: 5,
    text: "They were super easy to work with,",
    highlightText: "clear feedback",
    endText: "and helpful suggestions.",
    color: "text-lime-500",
    highlightColor: "text-lime-500",
    height: "h-48",
  },
  {
    name: "Harsh Vardhan",
    rating: 5,
    text: "No hidden charges and",
    highlightText: "transparent process",
    endText: "with result-focused output.",
    color: "text-cyan-500",
    highlightColor: "text-cyan-500",
    height: "h-44",
  },
];

export default function ReviewMarqueeHorizontal() {
  const column1 = [
    { ...reviews[0], height: "h-full md:h-48 lg:h-52" },
    { ...reviews[3], height: "h-full md:h-52 lg:h-56" },
  ];

  const column2 = [
    { ...reviews[1], height: "h-full md:h-44 lg:h-48" },
    { ...reviews[4], height: "h-full md:h-48 lg:h-52" },
    { ...reviews[5], height: "h-full md:h-44 lg:h-48" },
  ];

  const column3 = [
    { ...reviews[2], height: "h-full md:h-56 lg:h-60" },
    { ...reviews[6], height: "h-full md:h-40 lg:h-44" },
    { ...reviews[0], height: "h-full md:h-48 lg:h-52" },
  ];

  return (
    <div className="w-full flex flex-col items-center justify-center mt-8 sm:mt-10 md:mt-12 lg:mt-16 xl:mt-20 mb-4">
      <div className="dark:bg-[#0C0C0C] bg-[#F9F9F9] dark:shadow-none shadow-[0px_0px_5px_3px_#00000024] w-full sm:w-[90%] md:w-[85%] lg:w-[80%] py-8 sm:py-10 md:py-12 lg:py-14 xl:py-16 px-4 sm:px-6 md:px-8 overflow-hidden rounded-2xl sm:rounded-3xl md:rounded-[40px] lg:rounded-[50px] flex flex-col items-center justify-center">
        <div className="text-center mb-6 sm:mb-8 md:mb-10 lg:mb-12">
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-[65px] 2xl:text-[72px] font-bold leading-tight sm:leading-snug md:leading-normal lg:leading-[90px] tracking-tight sm:tracking-normal md:tracking-[-1px] dark:text-white text-gray-800 mb-2 sm:mb-3 md:mb-4">
            Join 3 million writers
          </h2>
          <p className="dark:text-white text-gray-800  text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl 2xl:text-[24px]">
            AGKraft is rating 4.9/5 with over 3000+ reviews online.
          </p>
        </div>

        <div className="relative  max-w-7xl mx-auto h-[240px] sm:h-[320px] md:h-[400px] lg:h-[500px] xl:h-[600px] overflow-hidden w-full rounded-lg">
          <div className="absolute top-0 left-0 right-0 h-8 sm:h-12 md:h-16 lg:h-20 bg-gradient-to-b from-[#0C0C0C] to-transparent z-10 pointer-events-none"></div>
          <div className="absolute bottom-0 left-0 right-0 h-8 sm:h-12 md:h-16 lg:h-20 bg-gradient-to-t from-[#0C0C0C] to-transparent z-10 pointer-events-none"></div>

          <div className="flex gap-2 sm:gap-3 md:gap-4 lg:gap-6 justify-center h-full px-2 sm:px-3 md:px-4">
            <div className="flex-1 max-w-xs sm:max-w-sm">
              <Marquee className="[--duration:40s] h-full" vertical pauseOnHover>
                {column1.concat(column1).map((review, index) => (
                  <div
                    key={index}
                    className={`p-4 sm:p-5 md:p-6 rounded-lg sm:rounded-xl w-full ${review.height} flex flex-col justify-between text-white mb-3 sm:mb-4 md:mb-5 lg:mb-6 cursor-pointer border border-[#282828] bg-[#242424]`}
                  >
                    <div>
                      <div className="flex gap-1 mb-2 sm:mb-3 md:mb-4">
                        {Array.from({ length: 5 }, (_, i) => (
                          <FaStar
                            key={i}
                            className={`text-xs sm:text-sm ${
                              i < review.rating ? review.color : "text-gray-600"
                            }`}
                          />
                        ))}
                      </div>
                      <p className="text-xs sm:text-sm leading-relaxed text-gray-300">
                        "{review.text}{" "}
                        <span className={review.highlightColor}>
                          {review.highlightText}
                        </span>{" "}
                        {review.endText}"
                      </p>
                    </div>
                    <p className="text-right text-xs sm:text-sm mt-2 sm:mt-3 md:mt-4 text-gray-400 font-medium">
                      {review.name}
                    </p>
                  </div>
                ))}
              </Marquee>
            </div>

            <div className="flex-1 max-w-xs sm:max-w-sm">
              <Marquee className="[--duration:50s] h-full" vertical reverse pauseOnHover>
                {column2.concat(column2).map((review, index) => (
                  <div
                    key={index}
                    className={`p-4 sm:p-5 md:p-6 rounded-lg sm:rounded-xl w-full ${review.height} flex flex-col justify-between text-white mb-3 sm:mb-4 md:mb-5 lg:mb-6 cursor-pointer border border-[#282828] bg-[#242424]`}
                  >
                    <div>
                      <div className="flex gap-1 mb-2 sm:mb-3 md:mb-4">
                        {Array.from({ length: 5 }, (_, i) => (
                          <FaStar
                            key={i}
                            className={`text-xs sm:text-sm ${
                              i < review.rating ? review.color : "text-gray-600"
                            }`}
                          />
                        ))}
                      </div>
                      <p className="text-xs sm:text-sm leading-relaxed text-gray-300">
                        "{review.text}{" "}
                        <span className={review.highlightColor}>
                          {review.highlightText}
                        </span>{" "}
                        {review.endText}"
                      </p>
                    </div>
                    <p className="text-right text-xs sm:text-sm mt-2 sm:mt-3 md:mt-4 text-gray-400 font-medium">
                      {review.name}
                    </p>
                  </div>
                ))}
              </Marquee>
            </div>

            <div className="flex-1 max-w-xs sm:max-w-sm">
              <Marquee className="[--duration:45s] h-full" vertical pauseOnHover>
                {column3.concat(column3).map((review, index) => (
                  <div
                    key={index}
                    className={`p-4 sm:p-5 md:p-6 rounded-lg sm:rounded-xl w-full ${review.height} flex flex-col justify-between text-white mb-3 sm:mb-4 md:mb-5 lg:mb-6 cursor-pointer border border-[#282828] bg-[#242424]`}
                  >
                    <div>
                      <div className="flex gap-1 mb-2 sm:mb-3 md:mb-4">
                        {Array.from({ length: 5 }, (_, i) => (
                          <FaStar
                            key={i}
                            className={`text-xs sm:text-sm ${
                              i < review.rating ? review.color : "text-gray-600"
                            }`}
                          />
                        ))}
                      </div>
                      <p className="text-xs sm:text-sm leading-relaxed text-gray-300">
                        "{review.text}{" "}
                        <span className={review.highlightColor}>
                          {review.highlightText}
                        </span>{" "}
                        {review.endText}"
                      </p>
                    </div>
                    <p className="text-right text-xs sm:text-sm mt-2 sm:mt-3 md:mt-4 text-gray-400 font-medium">
                      {review.name}
                    </p>
                  </div>
                ))}
              </Marquee>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
