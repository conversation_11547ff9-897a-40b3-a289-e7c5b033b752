'use client';

import Image from 'next/image';
import contactImage from '@/public/new-assests/proejct/subimage/table.png';
import React from 'react';
import { FaEnvelope, FaPhoneAlt, FaComments } from "react-icons/fa";
import ContactForm from './contactForm';
export default function ContactSection(): JSX.Element {
  return (
    <section className="bg-white dark:bg-black text-black dark:text-white transition-colors duration-300">
      {/* Header Image */}
      <div className="w-full">
        <Image
          src={contactImage}
          alt="Contact Us"
          className="w-full max-h-[400px] object-cover"
          priority
        />
      </div>

      {/* Contact Info Section */}
<section className="bg-white dark:bg-black text-black dark:text-white py-16 flex flex-col gap-5 transition-colors duration-300">
      <h2 className="text-center text-[72px] font-semibold leading-[80px] tracking-[-2px] text-black dark:text-white">Get in Touch</h2>
      <div className="flex flex-col md:flex-row justify-around
       items-center gap-12 px-4">
        {/* Email Contact */}
        <div className="flex flex-col items-center text-center">
          <div className="w-[64px]  flex items-center justify-center h-[64px] bg-white/20 hover:bg-[#FF640F] cursor-pointer p-4 rounded-full mb-4">
            <FaEnvelope  className='w-[64px] h-[64px] text-[10px]' />
          </div>
          <p className="mb-1 text-[24px] leading-[36px]">We’re always happy to help.</p>
          <p className="text-gray-700 dark:text-white text-[20px] leading-[100%] "><EMAIL></p>
        </div>

        {/* Phone Contact */}
        <div className="flex flex-col items-center text-center">
          <div className="w-[64px]  flex items-center justify-center h-[64px] bg-white/20 hover:bg-[#FF640F] cursor-pointer p-4 rounded-full mb-4">
            <FaPhoneAlt className='w-[64px] h-[64px] text-[10px]' />
          </div>
          <p className="mb-1 text-[24px] leading-[36px]">Our hotline number</p>
          <p className="text-gray-700 dark:text-white text-[20px] leading-[100%] ">
            +91 ************
          </p>
        </div>

        {/* Live Chat */}
      <div className="flex flex-col items-center text-center">
          <div className="w-[64px]  flex items-center justify-center h-[64px] bg-white/20 hover:bg-[#FF640F] cursor-pointer p-4 rounded-full mb-4">
            <FaComments size={5}  className='w-[64px] h-[64px] text-[10px]' />
          </div>
          <p className="mb-1 text-[24px] leading-[36px]">Live chat</p>
          <p className="text-gray-700 dark:text-white text-[20px] leading-[100%] ">https://agkraft.in/</p>
        </div>
      </div>
    </section>

    <ContactForm/>
    
    
    </section>
  );
}

// ✅ Types
interface ContactInfoProps {
  icon: React.ReactNode;
  label: string;
  value: string;
}

interface InputFieldProps {
  type: string;
  placeholder: string;
}

interface TextareaFieldProps {
  placeholder: string;
  rows: number;
}

interface SubmitButtonProps {
  label: string;
}

// ✅ Reusable Subcomponents

const ContactInfo: React.FC<ContactInfoProps> = ({ icon, label, value }) => {
  return (
    <div className="flex flex-col items-center space-y-2 max-w-xs">
      <div className="text-orange-500">{icon}</div>
      <p className="text-center text-sm leading-relaxed">
        {label}
        <br />
        <span className="font-medium text-white">{value}</span>
      </p>
    </div>
  );
};

const InputField: React.FC<InputFieldProps> = ({ type, placeholder }) => {
  return (
    <input
      type={type}
      placeholder={placeholder}
      className="w-full px-4 py-3 rounded bg-gray-800 text-white focus:outline-none"
    />
  );
};

const TextareaField: React.FC<TextareaFieldProps> = ({ placeholder, rows }) => {
  return (
    <textarea
      placeholder={placeholder}
      rows={rows}
      className="w-full px-4 py-3 rounded bg-gray-800 text-white focus:outline-none"
    />
  );
};

const SubmitButton: React.FC<SubmitButtonProps> = ({ label }) => {
  return (
    <button
      type="submit"
      className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded font-semibold transition w-full md:w-auto"
    >
      {label}
    </button>
  );
};
