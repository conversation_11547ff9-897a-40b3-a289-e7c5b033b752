// src/lib/apiClient.ts
import axios from "axios";

// Get base URL from environment variable
const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
console.log("🌐 API Client BASE_URL =", BASE_URL);

if (!BASE_URL) {
  throw new Error("NEXT_PUBLIC_API_BASE_URL environment variable is not defined");
}

// Create axios instance with common configuration
const apiClient = axios.create({
  baseURL: BASE_URL,
  timeout: 10000, // 10 seconds timeout
  headers: {
    'Content-Type': 'application/json',
  }
});

// Add interceptors if needed
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // You can add global error handling here
    return Promise.reject(error);
  }
);

export default apiClient;