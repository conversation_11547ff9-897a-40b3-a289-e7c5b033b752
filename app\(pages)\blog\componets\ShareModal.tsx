"use client";
import React, { useState } from "react";
import {
  FaFacebook,
  FaInstagram,
  FaL<PERSON>edin,
  <PERSON>a<PERSON><PERSON>tter,
  FaWhatsapp,
  FaCopy,
} from "react-icons/fa";
import { FiShare2, FiCheck } from "react-icons/fi";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { getSocialShareUrls } from "@/api/blogs/blogs_api";

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  blogId: string;
  blogTitle: string;
  blogUrl?: string;
}

export default function ShareModal({
  isOpen,
  onClose,
  blogId,
  blogTitle,
  blogUrl,
}: ShareModalProps) {
  const [copied, setCopied] = useState(false);
  const currentUrl =
    blogUrl || (typeof window !== "undefined" ? window.location.href : "");
  const shareUrls = getSocialShareUrls(blogId, blogTitle, currentUrl);

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(currentUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error("Failed to copy link:", error);
    }
  };

  const handleSocialShare = (platform: string) => {
    let url = "";
    switch (platform) {
      case "facebook":
        url = shareUrls.facebook;
        break;
      case "twitter":
        url = shareUrls.twitter;
        break;
      case "linkedin":
        url = shareUrls.linkedin;
        break;
      case "whatsapp":
        url = `https://wa.me/?text=${encodeURIComponent(
          `${blogTitle} - ${currentUrl}`
        )}`;
        break;
      case "instagram":
        handleCopyLink();
        return;
    }
    if (url) window.open(url, "_blank", "width=600,height=400");
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-zinc-900 border-zinc-700 max-w-xs sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-white text-lg sm:text-xl font-bold flex items-center gap-2">
            <FiShare2 className="w-4 h-4 sm:w-5 sm:h-5" />
            Share this article
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-3 sm:space-y-4">
          {/* Blog Title */}
          <div className="bg-zinc-800 rounded-lg p-2 sm:p-3">
            <p className="text-white/80 text-xs sm:text-sm line-clamp-2">
              {blogTitle}
            </p>
          </div>

          {/* Social Share Buttons */}
          <div className="grid grid-cols-2 gap-2 sm:gap-3">
            <button
              onClick={() => handleSocialShare("facebook")}
              className="flex items-center gap-2 p-2 sm:p-3 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors text-white text-xs sm:text-sm"
            >
              <FaFacebook className="w-4 h-4 sm:w-5 sm:h-5" />
              <span className="font-medium">Facebook</span>
            </button>

            <button
              onClick={() => handleSocialShare("twitter")}
              className="flex items-center gap-2 p-2 sm:p-3 bg-sky-500 hover:bg-sky-600 rounded-lg transition-colors text-white text-xs sm:text-sm"
            >
              <FaTwitter className="w-4 h-4 sm:w-5 sm:h-5" />
              <span className="font-medium">Twitter</span>
            </button>

            <button
              onClick={() => handleSocialShare("linkedin")}
              className="flex items-center gap-2 p-2 sm:p-3 bg-blue-700 hover:bg-blue-800 rounded-lg transition-colors text-white text-xs sm:text-sm"
            >
              <FaLinkedin className="w-4 h-4 sm:w-5 sm:h-5" />
              <span className="font-medium">LinkedIn</span>
            </button>

            <button
              onClick={() => handleSocialShare("whatsapp")}
              className="flex items-center gap-2 p-2 sm:p-3 bg-green-600 hover:bg-green-700 rounded-lg transition-colors text-white text-xs sm:text-sm"
            >
              <FaWhatsapp className="w-4 h-4 sm:w-5 sm:h-5" />
              <span className="font-medium">WhatsApp</span>
            </button>

            <button
              onClick={() => handleSocialShare("instagram")}
              className="flex items-center gap-2 p-2 sm:p-3 bg-pink-600 hover:bg-pink-700 rounded-lg transition-colors text-white text-xs sm:text-sm"
            >
              <FaInstagram className="w-4 h-4 sm:w-5 sm:h-5" />
              <span className="font-medium">Instagram</span>
            </button>

            <button
              onClick={handleCopyLink}
              className="flex items-center gap-2 p-2 sm:p-3 bg-zinc-700 hover:bg-zinc-600 rounded-lg transition-colors text-white text-xs sm:text-sm"
            >
              {copied ? (
                <>
                  <FiCheck className="w-4 h-4 sm:w-5 sm:h-5 text-green-400" />
                  <span className="font-medium text-green-400">Copied!</span>
                </>
              ) : (
                <>
                  <FaCopy className="w-4 h-4 sm:w-5 sm:h-5" />
                  <span className="font-medium">Copy Link</span>
                </>
              )}
            </button>
          </div>

          {/* URL Display */}
          <div className="bg-zinc-800 rounded-lg p-2 sm:p-3">
            <p className="text-white/60 text-xs mb-1">Link:</p>
            <p className="text-white/80 text-xs sm:text-sm break-all">
              {currentUrl}
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
