"use client";
import React from "react";
import Image from "next/image";

// Import team member images (you can replace these with actual team photos)

import aryan from "@/public/new-assests/news-icons/heroicons/homeimage/1.png";
import gulshan from "@/public/new-assests/news-icons/heroicons/homeimage/2.png";
import kunal from "@/public/new-assests/news-icons/heroicons/homeimage/3.png";

// icoms
import diamond from "@/public/new-assests/news-icons/heroicons/diamond.svg";
import twotriangle from "@/public/new-assests/news-icons/heroicons/twotriangle.svg";
import rectanglecategory from "@/public/new-assests/news-icons/heroicons/rectangle category.png";
import TeamHappyClientsCard from "./teamhappycard";

export default function HeroSectionCards() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-y-3 lg:grid-cols-5 xl:grid-cols-5 xl:gap-3 lg:gap-2 md:gap-4 px-10 max-w-full items-end">
      {/* Background Tech Image Card - First on mobile, last on desktop */}
      <div className="relative rounded-[20px] overflow-hidden xl:w-full lg:w-full md:w-full w-full xl:h-[564px] lg:h-[404px]  md:h-[455px]  h-[455px]   order-1 xl:order-1">
        <div className="absolute inset-0 bg-[url('/new-assests/news-icons/heroicons/header.png')]  bg-cover bg-center "></div>
        <div className="relative z-10 bg-black opacity-30 p-6 h-full flex items-end"></div>
      </div>

    {/* Card 2: Team & Happy Clients */}
    <div className="xl:h-[455px] overflow-hidden  xl:w-full lg:w-full md:w-full w-full  lg:h-[354px] xl:order-2 lg:order-2 md:order-2 order-2 ">
       <TeamHappyClientsCard/>
    </div>

      {/* Card 3: Successful Projects */}
      <div className="bg-[#2A2A2A] rounded-3xl p-6 text-white   xl:h-[353px]  xl:w-full lg:w-full md:w-full w-full  lg:h-[254px] md:h-[455px]   h-[455px]  flex flex-col justify-center items-center order-4 xl:order-3">
        <div className="flex flex-col items-center justify-center xl:gap-10 lg:gap-5 md:gap-10 -gap-5 ">
          <div className="w-[53px] h-[53px] bg-[#cfcdcd] rounded-full flex items-center justify-center">
            <Image
              src={diamond}
              alt="Icon"
              width={24}
              height={24}
              className="w-[25.5px] h-[20.4px]"
            />
          </div>
          <div className="text-center">
            <h3 className="xl:text-[70px] lg:text-[55px] md:text-[70px] text-[70px] font-bold mb-2">
              350+
            </h3>
            <p className="text-[24px] text-white opacity-70">
              Successful Projects
            </p>
          </div>
        </div>
      </div>

      {/* Card 4: Testimonial */}
      <div className="bg-[#FFA0F9] rounded-3xl xl:p-8 lg:p-3 md:p-8 p-8 text-white xl:h-[455px]  xl:w-full lg:w-full md:w-full w-full  lg:h-[354px]   md:h-[455px]   h-[455px]   flex flex-col justify-between order-5 xl:order-4">
        <div className="flex justify-start ">
          <div className="w-[51px] h-[51px] bg-[#EE76E6] rounded-full flex items-center justify-center">
            <Image
              src={twotriangle}
              alt="Icon"
              width={24}
              height={24}
              className="w-[14.25px] h-[15.4px]"
            />
          </div>
        </div>
        <div>
          <p className="xl:text-[24px] lg:text-[18px] md:text-[24px] text-[24px] xl:p-2   text-black leading-[32px] ">
            “Efficient, knowledgeable, & smooth experience. Highly recommended”
          </p>
        </div>
        <div>
          <p className="text-[20px]">
            <span className="font-semibold text-[#000000]">Harsul Arrora,</span>
            <span className="text-black opacity-[40%] text-[18px]">
              {" "}
              CEO Macgence
            </span>
          </p>
        </div>
      </div>

      {/* Card 1: Experience & Tech Stack */}
      <div className="bg-[#2A2A2A] rounded-3xl p-5 text-white  xl:h-[565px]  lg:h-[404px]    xl:w-full lg:w-full md:w-full w-full md:h-[500px]   h-[500px]  flex flex-col justify-between order-5 xl:order-5">
        <div>
          <h3 className="xl:text-[82px] lg:text-[55px] md:text-[82px] text-[82px]  text-white font-bold mb-2">
            07+
          </h3>
          <p className="xl:text-[28px] lg:text-[20px] md:text-[28px] text-[28px] text-white mb-4">
            Years Experience
            <br />
            In this Field.
          </p>
        </div>
        <div className="flex flex-wrap gap-2">
          <Image
            src={rectanglecategory}
            alt="Icon"
            width={24}
            height={24}
            className="xl:w-[258px] xl:h-[242.4px] lg:w-[265px] lg:h-[180.4px] md:w-[258px] md:h-[242.4px] w-[258px] h-[242.4px]"
          />
        </div>
      </div>
    </div>
  );
}
