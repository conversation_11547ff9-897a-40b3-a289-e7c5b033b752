"use client";
import React, { useState } from 'react';
import { FiChevronDown, FiChevronUp } from 'react-icons/fi';

interface CategoriesSectionProps {
  categories: string[];
  selectedCategory: string;
  setSelectedCategory: (category: string) => void;
  getCategoryCount: (category: string) => number;
}

const CategoriesSection: React.FC<CategoriesSectionProps> = ({ 
  categories, 
  selectedCategory, 
  setSelectedCategory,
  getCategoryCount 
}) => {
  const [isCategoriesOpen, setIsCategoriesOpen] = useState(false);

  return (
    <div className="lg:w-1/4">
      {/* Mobile Category Toggle Button */}
      <button
        onClick={() => setIsCategoriesOpen(!isCategoriesOpen)}
        className="lg:hidden w-full bg-[#18181B] rounded-lg p-4 flex items-center justify-between mb-4"
      >
        <h3 className="text-base font-semibold text-white">
          {selectedCategory === 'All' ? 'All Categories' : selectedCategory}
        </h3>
        {isCategoriesOpen ? (
          <FiChevronUp className="w-5 h-5 text-orange-500" />
        ) : (
          <FiChevronDown className="w-5 h-5 text-gray-400" />
        )}
      </button>

      {/* Categories Panel */}
      <div 
        className={`bg-[#18181B] rounded-lg p-4 sm:p-6 sticky top-4 sm:top-8 transition-all duration-300
          ${isCategoriesOpen ? 'block' : 'hidden lg:block'}
          ${isCategoriesOpen ? 'max-h-[500px] overflow-y-auto' : ''}
        `}
      >
        <h3 className="text-base sm:text-lg font-semibold text-white mb-3 sm:mb-4 hidden lg:block">
          Categories
        </h3>
        <div className="space-y-1 sm:space-y-2">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => {
                setSelectedCategory(category);
                setIsCategoriesOpen(false);
              }}
              className={`w-full text-left px-3 py-2 sm:px-4 sm:py-3 rounded-lg transition-colors flex items-center justify-between text-sm sm:text-base ${
                selectedCategory === category
                  ? 'bg-orange-600 text-white'
                  : 'text-gray-300 hover:bg-gray-800 hover:text-white'
              }`}
            >
              <span className="truncate">{category}</span>
              <span className={`text-xs sm:text-sm px-2 py-1 rounded-full ml-2 ${
                selectedCategory === category
                  ? 'bg-orange-700 text-white'
                  : 'bg-gray-700 text-gray-300'
              }`}>
                {getCategoryCount(category)}
              </span>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CategoriesSection;