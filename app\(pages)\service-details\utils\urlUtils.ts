/**
 * Utility functions for URL encoding and decoding
 */

/**
 * Encodes a string to be URL-safe
 * Converts spaces to hyphens and applies URL encoding
 * @param str - The string to encode
 * @returns URL-safe encoded string
 */
export const encodeUrlString = (str: string): string => {
  return encodeURIComponent(
    str
      .toLowerCase()
      .trim()
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/[^\w\-]/g, '') // Remove special characters except hyphens
  );
};

/**
 * Decodes a URL-safe string back to original format
 * @param str - The URL-safe string to decode
 * @returns Decoded string
 */
export const decodeUrlString = (str: string): string => {
  return decodeURIComponent(str.replace(/-/g, ' '));
};

/**
 * Generates a service URL with encoded parameters
 * @param id - Service ID
 * @param title - Service title
 * @returns Complete service details URL
 */
export const generateServiceUrl = (id: number, title: string): string => {
  const encodedTitle = encodeUrlString(title);
  return `/service-details/${id}/${encodedTitle}`;
};

/**
 * Validates if the URL name matches the expected service title
 * @param urlName - Name from URL parameters
 * @param serviceTitle - Actual service title
 * @returns Boolean indicating if they match
 */
export const validateUrlName = (urlName: string, serviceTitle: string): boolean => {
  const decodedUrlName = decodeUrlString(urlName);
  const normalizedServiceTitle = serviceTitle.toLowerCase().trim();
  return decodedUrlName.toLowerCase() === normalizedServiceTitle;
};
