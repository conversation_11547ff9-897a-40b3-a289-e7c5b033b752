// src/api/commentApi.ts
import apiClient from "@/lib/apiclient";

// Comment interface based on API response
export interface Comment {
    id: number;
    name: string;
    email: string;
    comment: string;
    status: 'pending' | 'approved' | 'rejected' | 'spam';
    isVisible: boolean;
    moderatedBy?: string;
    moderatedAt?: string;
    ipAddress: string;
    userAgent: string;
    createdAt: string;
    updatedAt: string;
    blogId?: string; // Added blogId to interface
    replyTo?: number; // Added for reply functionality
    replies?: Comment[]; // Added for nested comments
}

// API Response interface
export interface CommentApiResponse {
    status: boolean;
    code: number;
    message: string;
    data: Comment[];
    pagination?: {
        currentPage: number;
        totalPages: number;
        totalItems: number;
        itemsPerPage: number;
        hasNextPage: boolean;
        hasPrevPage: boolean;
    };
}

// Single Comment Response interface
export interface SingleCommentResponse {
    status: boolean;
    code: number;
    message: string;
    data: Comment;
}

// Comment Statistics interface
export interface CommentStatistics {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
    spam: number;
    visible: number;
    byBlog: Array<{ blogId: string; count: number }>; // Added blog-wise statistics
}

// Create Comment interface (Public)
export interface CreateCommentData {
    name: string;
    email: string;
    comment: string;
    blogId: string;
    replyTo?: number; // Added for reply functionality
    captchaToken?: string; // Added for CAPTCHA verification
}

// Update Comment interface (Admin)
export interface UpdateCommentData {
    status?: 'pending' | 'approved' | 'rejected' | 'spam';
    isVisible?: boolean;
    moderatedBy?: string;
    comment?: string; // Added ability to edit comment text
}

// NEW: Comment moderation settings interface
export interface CommentSettings {
    requireModeration: boolean;
    allowReplies: boolean;
    maxCommentLength: number;
    minCommentLength: number;
    allowLinks: boolean;
    requireCaptcha: boolean;
}

// Submit new comment (Public endpoint)
export const submitComment = async (
    commentData: CreateCommentData
): Promise<SingleCommentResponse> => {
    try {
        const response = await apiClient.post('/comments', commentData);
        return response.data;
    } catch (error: any) {
        console.error('Error submitting comment:', error);
        
        // Enhanced error handling
        if (error.response) {
            throw new Error(error.response.data?.message || 'Failed to submit comment');
        } else if (error.request) {
            throw new Error('Network error. Please check your connection and try again.');
        } else {
            throw new Error('An unexpected error occurred. Please try again later.');
        }
    }
};

// Get approved comments (Public endpoint)
export const getApprovedComments = async (params?: {
    page?: number;
    limit?: number;
    blogId?: string;
    includeReplies?: boolean; // NEW: Option to include replies
}): Promise<CommentApiResponse> => {
    try {
        const queryParams = new URLSearchParams();

        if (params?.page) queryParams.append('page', params.page.toString());
        if (params?.limit) queryParams.append('limit', params.limit.toString());
        if (params?.blogId) queryParams.append('blogId', params.blogId);
        if (params?.includeReplies) queryParams.append('includeReplies', 'true');

        const url = `/comments/approved${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const response = await apiClient.get(url);

        return response.data;
    } catch (error: any) {
        console.error('Error fetching approved comments:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch comments');
    }
};

// Get all comments with optional filters (Admin endpoint)
export const getAllComments = async (params?: {
    page?: number;
    limit?: number;
    status?: string;
    isVisible?: boolean;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    blogId?: string; // NEW: Filter by blog ID
}): Promise<CommentApiResponse> => {
    try {
        const queryParams = new URLSearchParams();
        
        if (params?.page) queryParams.append('page', params.page.toString());
        if (params?.limit) queryParams.append('limit', params.limit.toString());
        if (params?.status) queryParams.append('status', params.status);
        if (params?.isVisible !== undefined) queryParams.append('isVisible', params.isVisible.toString());
        if (params?.search) queryParams.append('search', params.search);
        if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
        if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);
        if (params?.blogId) queryParams.append('blogId', params.blogId);

        const url = `/comments${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const response = await apiClient.get(url);
        
        return response.data;
    } catch (error: any) {
        console.error('Error fetching all comments:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch comments');
    }
};

// Get comment by ID (Admin endpoint)
export const getCommentById = async (id: number): Promise<SingleCommentResponse> => {
    try {
        const response = await apiClient.get(`/comments/${id}`);
        return response.data;
    } catch (error: any) {
        console.error('Error fetching comment by ID:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch comment');
    }
};

// Update comment (Admin endpoint - for moderation)
export const updateComment = async (
    id: number, 
    commentData: UpdateCommentData
): Promise<SingleCommentResponse> => {
    try {
        const response = await apiClient.put(`/comments/${id}`, commentData);
        return response.data;
    } catch (error: any) {
        console.error('Error updating comment:', error);
        throw new Error(error.response?.data?.message || 'Failed to update comment');
    }
};

// Delete comment by ID (Admin endpoint)
export const deleteComment = async (id: number): Promise<{ status: boolean; message: string }> => {
    try {
        const response = await apiClient.delete(`/comments/${id}`);
        return response.data;
    } catch (error: any) {
        console.error('Error deleting comment:', error);
        throw new Error(error.response?.data?.message || 'Failed to delete comment');
    }
};

// NEW: Bulk update comments (Admin endpoint)
export const bulkUpdateComments = async (
    ids: number[],
    updateData: UpdateCommentData
): Promise<{ status: boolean; message: string; updatedCount: number }> => {
    try {
        const response = await apiClient.patch('/comments/bulk', { ids, updateData });
        return response.data;
    } catch (error: any) {
        console.error('Error in bulk update:', error);
        throw new Error(error.response?.data?.message || 'Failed to bulk update comments');
    }
};

// Get comment statistics (Admin endpoint)
export const getCommentStatistics = async (): Promise<{ 
    status: boolean; 
    data: CommentStatistics 
}> => {
    try {
        const response = await apiClient.get('/comments/statistics');
        return response.data;
    } catch (error: any) {
        console.error('Error fetching comment statistics:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch statistics');
    }
};

// NEW: Get comment settings
export const getCommentSettings = async (): Promise<CommentSettings> => {
    try {
        const response = await apiClient.get('/comments/settings');
        return response.data;
    } catch (error: any) {
        console.error('Error fetching comment settings:', error);
        // Return default settings if API fails
        return {
            requireModeration: true,
            allowReplies: true,
            maxCommentLength: 1000,
            minCommentLength: 10,
            allowLinks: false,
            requireCaptcha: true
        };
    }
};

// NEW: Update comment settings
export const updateCommentSettings = async (
    settings: Partial<CommentSettings>
): Promise<CommentSettings> => {
    try {
        const response = await apiClient.put('/comments/settings', settings);
        return response.data;
    } catch (error: any) {
        console.error('Error updating comment settings:', error);
        throw new Error(error.response?.data?.message || 'Failed to update settings');
    }
};

// Approve comment (Admin helper function)
export const approveComment = async (
    id: number, 
    moderatedBy: string
): Promise<SingleCommentResponse> => {
    try {
        return await updateComment(id, {
            status: 'approved',
            isVisible: true,
            moderatedBy
        });
    } catch (error: any) {
        console.error('Error approving comment:', error);
        throw error;
    }
};

// Reject comment (Admin helper function)
export const rejectComment = async (
    id: number, 
    moderatedBy: string
): Promise<SingleCommentResponse> => {
    try {
        return await updateComment(id, {
            status: 'rejected',
            isVisible: false,
            moderatedBy
        });
    } catch (error: any) {
        console.error('Error rejecting comment:', error);
        throw error;
    }
};

// Mark comment as spam (Admin helper function)
export const markCommentAsSpam = async (
    id: number, 
    moderatedBy: string
): Promise<SingleCommentResponse> => {
    try {
        return await updateComment(id, {
            status: 'spam',
            isVisible: false,
            moderatedBy
        });
    } catch (error: any) {
        console.error('Error marking comment as spam:', error);
        throw error;
    }
};

// NEW: Get replies for a comment
export const getCommentReplies = async (
    commentId: number
): Promise<CommentApiResponse> => {
    try {
        const response = await apiClient.get(`/comments/${commentId}/replies`);
        return response.data;
    } catch (error: any) {
        console.error('Error fetching comment replies:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch replies');
    }
};

// Utility function to format comment date
export const formatCommentDate = (dateString: string): string => {
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        console.error('Error formatting date:', error);
        return dateString;
    }
};

// Utility function to get comment status color
export const getCommentStatusColor = (status: string): string => {
    switch (status) {
        case 'approved': return 'text-green-500';
        case 'rejected': return 'text-red-500';
        case 'spam': return 'text-orange-500';
        case 'pending': return 'text-yellow-500';
        default: return 'text-gray-500';
    }
};

// NEW: Validate comment before submission
export const validateComment = (comment: string, settings: CommentSettings): { 
    isValid: boolean; 
    error?: string 
} => {
    if (comment.length < settings.minCommentLength) {
        return {
            isValid: false,
            error: `Comment must be at least ${settings.minCommentLength} characters`
        };
    }

    if (comment.length > settings.maxCommentLength) {
        return {
            isValid: false,
            error: `Comment must be less than ${settings.maxCommentLength} characters`
        };
    }

    return { isValid: true };
};

export default {
    submitComment,
    getApprovedComments,
    getAllComments,
    getCommentById,
    updateComment,
    deleteComment,
    bulkUpdateComments,
    getCommentStatistics,
    getCommentSettings,
    updateCommentSettings,
    approveComment,
    rejectComment,
    markCommentAsSpam,
    getCommentReplies,
    formatCommentDate,
    getCommentStatusColor,
    validateComment
};