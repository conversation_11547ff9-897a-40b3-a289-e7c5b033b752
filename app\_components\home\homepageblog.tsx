"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import arrowblog from "@/public/new-assests/news-icons/heroicons/homeimage/arrowcircleblog.svg";
import Image from "next/image";
import {
  getBlogsByViews,
  formatBlogDate,
  BlogData,
} from "@/api/blogs/blogs_api";

export default function HomePageBlog() {
  const router = useRouter();
  const [blogs, setBlogs] = useState<BlogData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchBlogs();
  }, []);

  const fetchBlogs = async () => {
    try {
      setLoading(true);
      const response = await getBlogsByViews(4); // Get top 4 blogs by views to have fallbacks
      if (response.status) {
        // Filter blogs to ensure they have valid data and take only 2
        const validBlogs = response.data.data.filter(blog =>
          blog.title &&
          blog.title.trim() !== "" &&
          blog._id
        ).slice(0, 2);
        setBlogs(validBlogs);
      }
    } catch (error) {
      console.error("Error fetching blogs:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleBlogClick = (blogId: string, title: string) => {
    const slug = title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
    router.push(`/blog/${blogId}/${slug}`);
  };

  const handleExploreMore = () => {
    router.push("/blog");
  };

  return (
    <div className="text-white w-full flex flex-col justify-center items-center px-4 sm:px-6 lg:px-0">
      <div className="w-full max-w-7xl flex flex-col gap-y-8 md:gap-y-10 lg:gap-y-12 mt-8 md:mt-10">
        {/* Header Section */}
        <div className="flex flex-col md:flex-row justify-between items-center gap-6 md:gap-0">
          <div>
            <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-[78px] font-bold font-satoshi leading-tight">
              <span className="p-2 sm:p-3 md:p-4 bg-[#2B2B2B] rounded-tl-xl rounded-tr-xl rounded-br-xl">
                Latest Insights
              </span>
              <br />
              <span className="px-2 sm:px-3 md:px-4 bg-[#2B2B2B] rounded-tl-xl rounded-tr-xl rounded-br-xl">
                & News
              </span>
            </h2>
          </div>
          <div>
            <button
              onClick={handleExploreMore}
              className="w-full md:w-auto border border-gray-800 dark:border-white hover:border-[#F97316] dark:text-white text-gray-800 hover:text-white hover:bg-[#F97316] text-[16px] sm:text-[18px] md:text-[20px] lg:text-[24px] px-4 sm:px-5 md:px-6 py-2 sm:py-3 rounded-full font-medium transition-colors"
            >
              Explore More
            </button>
          </div>
        </div>

        {/* Blog Grid */}
        {loading ? (
          <div className="text-center py-10 md:py-20">
            <div className="text-white dark:text-white light:text-black text-lg md:text-xl">
              Loading blogs...
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 md:gap-[30px] lg:gap-[48px]">
            {blogs.map((blog) => (
              <div
                key={blog._id}
                onClick={() => handleBlogClick(blog._id, blog.title)}
                className="bg-[#ffffff] dark:bg-[#2B2B2B] border border-[#2B2B2B] rounded-2xl sm:rounded-[30px] cursor-pointer p-4 sm:p-6 flex flex-col justify-between relative overflow-hidden transition-all duration-300"
              >
                {/* Blog Image */}
                <div className="relative h-48 sm:h-64 md:h-80 lg:h-[381px] rounded-xl sm:rounded-[30px] overflow-hidden mb-4 sm:mb-6">
                  {blog.imageUrl && blog.imageUrl.trim() !== "" ? (
                    <Image
                      src={blog.imageUrl}
                      alt={blog.title}
                      fill
                      className="object-cover w-full h-full"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center">
                      <div className="text-center text-white/60">
                        <svg
                          className="w-16 h-16 mx-auto mb-2"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <p className="text-sm">No Image Available</p>
                      </div>
                    </div>
                  )}
                  {/* Arrow Icon - Bottom Right */}
                  <div className="absolute bottom-2 right-2 sm:bottom-4 sm:right-4">
                    <Image
                      src={arrowblog}
                      alt="Arrow"
                      width={50}
                      height={50}
                      className="w-10 h-10 sm:w-[61.45px] sm:h-[60.69px]"
                    />
                  </div>
                </div>

                {/* Blog Content */}
                <div className="flex flex-col justify-between gap-y-3 sm:gap-y-4 md:gap-y-5">
                  {/* Date and Views Info */}
                  <div className="flex flex-row justify-between items-center">
                    <div className="flex flex-row justify-center items-center gap-2 sm:gap-3">
                      <p className="dark:text-white/60 text-gray-800 text-sm sm:text-base md:text-[18px]">
                        {formatBlogDate(blog.createdAt)}
                      </p>
                      <p className="dark:text-white/60 text-gray-800 text-sm sm:text-base md:text-[18px]">
                        •
                      </p>
                      <p className="dark:text-white/60 text-gray-800 text-sm sm:text-base md:text-[18px]">
                        {blog.views || 0} views
                      </p>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-xl sm:text-2xl md:text-3xl lg:text-[32px] font-bold dark:text-white text-gray-800 leading-tight mb-2 sm:mb-3">
                      {blog.title}
                    </h3>
                  </div>

                  {/* Categories */}
                  <div className="flex flex-wrap gap-1 sm:gap-2">
                    {blog.category.slice(0, 2).map((cat, index) => (
                      <span
                        key={index}
                        className="bg-orange-500 text-white px-2 py-0.5 sm:px-3 sm:py-1 rounded-full text-xs sm:text-sm"
                      >
                        {cat}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
