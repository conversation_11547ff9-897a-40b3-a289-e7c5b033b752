// src/api/contactApi.ts
import apiClient from "@/lib/apiclient";



// Interface for contact form data
export interface ContactFormData {
    name: string;
    email: string;
    countryCode: string;
    phoneNumber: string;
    service: string;
    message: string;
}

// Interface for API response
export interface ContactApiResponse {
    success: boolean;
    message: string;
    data?: any;
    errors?: string[];
}

// Validation function
export function validateContactData(data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data.name || typeof data.name !== 'string' || data.name.trim().length < 2) {
        errors.push('Name is required and must be at least 2 characters long');
    }

    if (!data.email || typeof data.email !== 'string') {
        errors.push('Email is required');
    } else {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.email)) {
            errors.push('Please provide a valid email address');
        }
    }

    if (!data.countryCode || typeof data.countryCode !== 'string') {
        errors.push('Country code is required');
    }

    if (!data.phoneNumber || typeof data.phoneNumber !== 'string' || data.phoneNumber.trim().length === 0) {
        errors.push('Phone number is required');
    } else {
        const phoneRegex = /^[0-9+\-\s()]{6,20}$/;
        if (!phoneRegex.test(data.phoneNumber)) {
            errors.push('Please provide a valid phone number');
        }
    }

    if (!data.service || typeof data.service !== 'string' || data.service.trim().length < 2) {
        errors.push('Service is required');
    }

    if (!data.message || typeof data.message !== 'string' || data.message.trim().length < 10) {
        errors.push('Message is required and must be at least 10 characters long');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

// Submit contact form to backend
export const submitContactForm = async (formData: ContactFormData): Promise<ContactApiResponse> => {
    try {
        // Validate data before sending
        const validation = validateContactData(formData);
        if (!validation.isValid) {
            return {
                success: false,
                message: 'Validation failed',
                errors: validation.errors
            };
        }

        // Prepare clean data
        const cleanData: ContactFormData = {
            name: formData.name.trim(),
            email: formData.email.trim().toLowerCase(),
            countryCode: formData.countryCode,
            phoneNumber: formData.phoneNumber.replace(/\s+/g, ''),
            service: formData.service.trim(),
            message: formData.message.trim()
        };

    
        // Send to backend API
        const response = await apiClient.post('/contacts', cleanData);

          return {
            success: true,
            message: 'Contact form submitted successfully!',
            data: response.data
        };

    }catch (error: any) {
        console.error('Contact API error:', error);

        // Handle different types of errors
        if (error.response) {
            // Backend returned an error response
            return {
                success: false,
                message: error.response.data?.message || 'Failed to submit contact form',
                errors: error.response.data?.errors || []
            };
        } else if (error.request) {
            // Network error
            return {
                success: false,
                message: 'Network error. Please check your connection and try again.'
            };
        } else {
            // Other error
            return {
                success: false,
                message: 'An unexpected error occurred. Please try again later.'
            };
        }
    }
};

// Get contact form configuration (optional)
export const getContactConfig = async () => {
    try {
        const response = await apiClient.get('/contact/config');
        return {
            success: true,
            data: response.data
        };
    } catch (error) {
        console.error('Failed to get contact config:', error);
        return {
            success: false,
            message: 'Failed to load contact configuration'
        };
    }
};

// Export default
export default {
    submitContactForm,
    getContactConfig,
    validateContactData
};
