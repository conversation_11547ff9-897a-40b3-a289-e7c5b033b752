"use client";

import React from 'react';
import { useScrollAnimation, UseScrollAnimationOptions } from '@/hooks/useScrollAnimation';

interface AnimatedSectionProps extends UseScrollAnimationOptions {
  children: React.ReactNode;
  animation?: 'fade-in-up' | 'fade-in-left' | 'fade-in-right' | 'fade-in' | 'scale-in' | 'slide-in-bottom';
  delay?: number;
  className?: string;
}

export const AnimatedSection: React.FC<AnimatedSectionProps> = ({
  children,
  animation = 'fade-in-up',
  delay = 0,
  className = '',
  ...options
}) => {
  const { elementRef, isVisible } = useScrollAnimation({ delay, ...options });

  return (
    <div
      ref={elementRef}
      className={`animate-${animation} ${isVisible ? 'in-view' : ''} ${className}`}
    >
      {children}
    </div>
  );
};
