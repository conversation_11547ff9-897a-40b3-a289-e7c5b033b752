{"name": "agkraft", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next export"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@hookform/resolvers": "^3.9.0", "@mui/icons-material": "^6.1.0", "@mui/material": "^6.1.0", "@next/font": "^14.2.15", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.1", "@react-three/fiber": "^8.17.9", "@studio-freight/lenis": "^1.0.42", "@tabler/icons-react": "^3.19.0", "@types/three": "^0.169.0", "aos": "^2.3.4", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "dompurify": "^3.2.6", "dotenv": "^16.4.5", "embla-carousel": "^8.5.2", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-react": "^8.6.0", "framer-motion": "^11.12.0", "lucide-react": "^0.445.0", "next": "^15.4.3", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "react-redux": "^9.1.2", "react-slick": "^0.30.2", "react-toastify": "^10.0.6", "redux-persist": "^6.0.0", "sass": "^1.80.3", "sharp": "^0.33.5", "slick-carousel": "^1.8.1", "swiper": "^11.1.14", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "three": "^0.169.0", "yet-another-react-lightbox": "^3.21.6", "yup": "^1.4.0", "zod": "^3.24.1"}, "devDependencies": {"@types/aos": "^3.0.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-router-dom": "^5.3.3", "@types/react-slick": "^0.23.13", "eslint": "^8", "eslint-config-next": "14.2.7", "postcss": "^8", "tailwindcss": "^3.4.13", "typescript": "^5"}}