"use client";
import commandclientaboutus from "@/public/new-assests/news-icons/heroicons/homeimage/commandclientaboutus.png";
import { FaPlus, FaMinus } from "react-icons/fa";
import { useState } from "react";

// Define the accordion data as an array of objects
const accordionData = [
  {
    title: "Our mission for business",
    content:
      "At AGKraft, our mission is to help businesses grow digitally with smart, scalable, and custom-built solutions. We aim to turn ideas into powerful digital products that deliver real value.",
  },
  {
    title: "What's our goal",
    content:
      "Our goal is to make technology simple and accessible for every business. From startups to enterprises, we focus on creating reliable, high-performance websites, apps, and digital platforms that truly make a difference.",
  },
  {
    title: "Our vision",
    content:
      "We aim to become a leading tech partner for innovative brands across the globe. Our vision is to create future-ready digital experiences that help businesses thrive in a fast-changing world.",
  },
];

export default function AboutValue() {
  const [openAccordion, setOpenAccordion] = useState<number | null>(null);

  const toggleAccordion = (index: number) => {
    setOpenAccordion(openAccordion === index ? null : index);
  };

  return (
    <div className="w-full dark:text-white text-black flex justify-center items-center py-10 md:py-20">
      <div className="flex flex-col lg:flex-row w-[90%] max-w-[1400px] gap-10 items-center justify-between">
        {/* Left Section with Image - Order changes on mobile */}
        <div className="flex-1 relative order-2 lg:order-1 w-full">
          <img
            src={commandclientaboutus.src}
            alt="Team working"
            className="w-full h-auto max-w-[659px] mx-auto lg:h-[663px] object-cover"
          />
          <div className="absolute bottom-5 right-5 lg:right-[5rem] bg-white text-black p-2 lg:p-4 text-center">
            <h3 className="text-4xl lg:text-[64px]">07+</h3>
            <span className="text-sm lg:text-[20px] mt-1">
              Years experience <br /> with proud.
            </span>
          </div>
        </div>

        {/* Right Section with Tagline, Headline, and Accordion */}
        <div className="flex flex-col justify-between w-full lg:w-1/2 gap-8 lg:gap-20 order-1 lg:order-2">
          <div>
            <div className="uppercase text-sm md:text-[20px] leading-[100%] tracking-[3px] dark:text-white text-black opacity-60 mb-2">
              Our Values
            </div>
            <div className="text-4xl md:text-5xl lg:text-[72px] font-bold leading-[1.1] lg:leading-[74px] tracking-[-1px] lg:tracking-[-2px] mb-5">
              Helping businesses grow with{" "}
              <span className="text-white">expertise.</span>
            </div>
          </div>
          <div className="mt-0 lg:mt-5">
            {accordionData.map((item, index) => (
              <div key={index} className="border-b border-gray-700 flex flex-col gap-4">
                <div
                  className="flex justify-between items-center py-4 cursor-pointer"
                  onClick={() => toggleAccordion(index)}
                >
                  <span className="text-lg md:text-xl lg:text-[28px] leading-[100%] tracking-[-0.5px] lg:tracking-[-1px]">
                    {item.title}
                  </span>
                  {openAccordion === index ? (
                    <FaMinus className="text-xl lg:text-[28px] dark:text-white text-black" />
                  ) : (
                    <FaPlus className="text-xl lg:text-[28px] dark:text-white text-black" />
                  )}
                </div>
                <div
                  className={`text-sm dark:text-gray-400 text-gray-700 transition-all duration-500 ease-in-out overflow-hidden ${
                    openAccordion === index
                      ? "max-h-96 opacity-100"
                      : "max-h-0 opacity-0"
                  }`}
                >
                  <div className="py-4 lg:py-8 text-base md:text-lg lg:text-[22px] leading-relaxed lg:leading-[42px] tracking-[-0.5px] lg:tracking-[-1px]">
                    {item.content}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
