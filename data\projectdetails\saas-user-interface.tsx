// SAAS User Interface Project Details
import img2 from "@/public/new assests/proejct/maicourt.png"

export const saasUserInterfaceDetails = {
  id: "02",
  uniqueId: "saas-user-interface",
  title: "SAAS User Interface Design",
  category: "Web Application",
  overview: {
    description: "Developed a comprehensive SAAS platform interface that prioritizes user experience and business efficiency. The design focuses on creating intuitive workflows for complex business processes while maintaining a clean and professional aesthetic.",
    description2: "Creating a user-friendly interface for complex business workflows while ensuring scalability and maintaining consistent design patterns across multiple modules.",
    
  },
  projectDetails: {
    client: "Enterprise Software Company",
    duration: "4 Months",
    categories: "SAAS Platform",
    website: "www.SAAS.com",
  },
  processSteps: [
    {
      step: "02",
      title: "Process & Challenge",
      description: "The design process focused on simplifying complex enterprise workflows:",
      description2: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et.",
      points: [
        "Streamlining multi-step processes into intuitive flows",
        "Creating consistent design patterns across modules",
        "Ensuring accessibility compliance for enterprise standards",
        "Optimizing for both desktop and tablet experiences"
      ]
    },
    {
      step: "03",
      title: "Summary",
      description: "The final SAAS platform successfully reduced user onboarding time by 60% and improved overall user satisfaction scores. The modular design system enables rapid feature development and consistent user experiences."
    }
  ],
  images: {
    hero: img2,
    gallery: [img2, img2, img2]
  },
  technologies: ["React", "TypeScript", "Node.js", "PostgreSQL"],
  features: [
    "Dashboard Analytics",
    "Real-time Collaboration",
    "Advanced Filtering",
    "Export Capabilities",
    "Role-based Access"
  ],
  nextProject: {
    id: "03",
    title: "Mobile Application Interface",
    slug: "mobile-application-interface"
  },
  previousProject: {
    id: "01",
    title: "Mobile App UI Design",
    slug: "mobile-app-ui-design"
  }
};
