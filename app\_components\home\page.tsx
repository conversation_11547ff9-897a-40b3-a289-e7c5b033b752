import React from "react";
import HeroSection from "./herosection";
import HomeAbout from "./homeabout";
import OurServices from "./ourservices";
import OurLatestProject from "./ourlatestproejct";
import HomeReview from "./homereview";
import HomePageBlog from "./homepageblog";
import ContactUsHome from "../common/contactusHome";
import ScrollAnimationWrapper from "@/components/animations/ScrollAnimationWrapper";

export default function HomePage() {
  return (
    <div>
      <ScrollAnimationWrapper animation="fade-in">
        <HeroSection />
      </ScrollAnimationWrapper>

      <ScrollAnimationWrapper animation="fade-in-up" delay={200}>
        <HomeAbout />
      </ScrollAnimationWrapper>

      <ScrollAnimationWrapper animation="fade-in-up" delay={300}>
        <OurServices />
      </ScrollAnimationWrapper>

      <ScrollAnimationWrapper animation="fade-in-up" delay={200}>
        <OurLatestProject />
      </ScrollAnimationWrapper>

      <ScrollAnimationWrapper animation="scale-in" delay={300}>
        <HomeReview />
      </ScrollAnimationWrapper>

      {/* Blog section without animation as requested */}
      <HomePageBlog />

      {/* Contact section without animation as requested */}
      <ContactUsHome />
    </div>
  );
}
