import apiClient from "@/lib/apiclient";

// Service interface based on API response
export interface Service {
    _id: string;
    title: string;
    description: string;
    iconImageUrl: string;
    iconBgColor: string;
    videoUrl: string;
    serviceDescription: string;
    importantPoints: string[];
    questionsAnswers: {
        question: string;
        answer: string;
        order: number;
        _id: string;
    }[];
    status: string;
    category: string;
    tags: string[];
    views: number;
    featured: boolean;
    createdAt: string;
    updatedAt: string;
    id: number;
    __v: number;
}

// API Response interface
export interface ServicesResponse {
    data: {
        services: Service[];
    };
}

export interface ServiceByIdResponse {
    status: boolean;
    code: number;
    message: string;
    data: Service; // The service data is directly in data
}

// Get all services
export const getAllServices = async (): Promise<Service[]> => {
    try {
        const response = await apiClient.get<ServicesResponse>('/services');
        return response.data.data.services;
    } catch (error: any) {
        console.error('Error fetching services:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch services');
    }
};  


// Get service by ID
export const getServiceById = async (id: string | number): Promise<Service> => {
    try {
        const response = await apiClient.get<ServiceByIdResponse>(`/services/${id}`);
        return response.data.data;
    } catch (error: any) {
        console.error('Error fetching service by ID:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch service');
    }
};

// Utility function to truncate service description to 10 words
export const truncateDescription = (description: string, wordLimit: number = 10): string => {
    const words = description.split(' ');
    if (words.length <= wordLimit) {
        return description;
    }
    return words.slice(0, wordLimit).join(' ') + '...';
};

// Helper function to strip HTML tags and truncate
const stripHtmlAndTruncate = (htmlString: string, wordLimit: number): string => {
    // Remove HTML tags
    const textOnly = htmlString.replace(/<[^>]*>/g, '');
    // Decode HTML entities
    const decodedText = textOnly
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/&nbsp;/g, ' ');

    const words = decodedText.split(' ');
    if (words.length <= wordLimit) {
        return decodedText;
    }
    return words.slice(0, wordLimit).join(' ') + '...';
};

// Transform API service to card format for UI
export const transformServiceToCard = (service: Service) => {
    return {
        id: service._id, // Using _id as the main identifier
        title: service.title,
        description: stripHtmlAndTruncate(service.serviceDescription, 10),
        icon: service.iconImageUrl,
        iconBg: service.iconBgColor,
        _id: service._id
    };
};
