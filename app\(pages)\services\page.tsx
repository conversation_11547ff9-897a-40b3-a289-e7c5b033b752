"use client";
import HomeReview from "@/app/_components/home/<USER>";
import { CeoReview } from "./components/ceoreview";
import { DigitalBeast } from "./components/digitalbeast";
import { ServiceCards } from "./components/servicecards";
import ServicesStates from "./components/servicesstates";
import ScrollAnimationWrapper from "@/components/animations/ScrollAnimationWrapper";

export default function ServicesPage() {
  return (
    <div className="overflow-hidden  transition-colors duration-300">
      <ScrollAnimationWrapper animation="fade-in-up">
        <ServiceCards />
      </ScrollAnimationWrapper>

      <ScrollAnimationWrapper animation="fade-in-left" delay={200}>
        <CeoReview />
      </ScrollAnimationWrapper>

      <ScrollAnimationWrapper animation="scale-in" delay={300}>
        <ServicesStates />
      </ScrollAnimationWrapper>

      <ScrollAnimationWrapper animation="fade-in-right" delay={200}>
        <DigitalBeast />
      </ScrollAnimationWrapper>

      <ScrollAnimationWrapper animation="fade-in-up" delay={300}>
        <HomeReview />
      </ScrollAnimationWrapper>
    </div>
  );
}
