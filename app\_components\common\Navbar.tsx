"use client";
import logo from "@/public/new-assests/news-icons/heroicons/logo.png";
import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";
import { AiOutlineMenu, AiOutlineClose } from "react-icons/ai";
import ThemeToggle from "./ThemeToggle";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <nav
      className={`w-full flex flex-row rounded-[100px] sticky top-0 left-0 right-0 z-[150]`}
    >
      <div
        className={` w-full rounded-[100px] ${
          isScrolled ? "mx-2 my-2 bg-white dark:bg-[#1D1B1B] shadow-lg" : "m-4 bg-[#f9f9f9] dark:bg-[#1D1B1B]"
        }`}
      >
        <div className="flex items-center justify-between px-2">
          {/* logo image */}
          <Link href={"/"} className="p-2 px-4">
            <Image
              src={logo}
              alt="logo"
              className="w-[4.5rem] md:w-[4rem]"
              width={144}
              height={80}
            />
          </Link>
          {/* tabs */}

          {/* Navigation Links */}
          <div
            className={`hidden md:flex gap-8
            `}
          >
            <Link
              href="/"
              className="text-black dark:text-white text-[22px] leading-[46px] hover:text-[#FF640F] transition-all duration-300 ease-in-out  sm:text-center"
            >
              Home
            </Link>
            <Link
              href="/about"
              className="text-black dark:text-white text-[22px] leading-[46px]   hover:text-[#FF640F] transition-all duration-300 ease-in-out  sm:text-center"
            >
              About Us
            </Link>
            <Link
              href="/services"
              className="text-black dark:text-white text-[22px] leading-[46px] hover:text-[#FF640F] transition-all duration-300 ease-in-out  sm:text-center"
            >
              Services
            </Link>
            <Link
              href="/contact"
              className="text-black dark:text-white text-[22px] leading-[46px] hover:text-[#FF640F] transition-all duration-300 ease-in-out sm:text-center"
            >
              Contact
            </Link>
            <Link
              href="/blog"
              className="text-black dark:text-white text-[22px] leading-[46px] hover:text-[#FF640F] transition-all duration-300 ease-in-out  sm:text-center"
            >
              Blog
            </Link>
          </div>

          {/* Theme Toggle - Desktop */}
          <div className="md:flex md:flex-row hidden items-center gap-4">
            <ThemeToggle />
          </div>

          {/* Mobile Section */}
          <div className="md:hidden flex items-center gap-3">
            {/* Theme Toggle - Mobile */}
            <ThemeToggle />
            {/* Hamburger Icon for Mobile */}
            <div>
              {isMenuOpen ? (
                <AiOutlineClose
                  className="text-red-500 text-3xl cursor-pointer"
                  onClick={() => setIsMenuOpen(false)}
                />
              ) : (
                <AiOutlineMenu
                  className="text-white dark:text-white light:text-black text-3xl cursor-pointer"
                  onClick={() => setIsMenuOpen(true)}
                />
              )}
            </div>
          </div>

          {/* Mobile menu */}
          {isMenuOpen && (
            <div className="md:hidden absolute top-full left-0 w-full bg-[#17181B] dark:bg-[#17181B] light:bg-white text-white dark:text-white light:text-black flex flex-col items-center space-y-4 py-6 z-50 rounded-b-[20px] shadow-lg">
              <Link
                href="/"
                onClick={toggleMenu}
                className="text-white dark:text-white light:text-black text-[18px] sm:text-[20px] leading-[40px] hover:text-[#FF640F] transition-all duration-300 ease-in-out"
              >
                Home
              </Link>
              <Link
                href="/about"
                onClick={toggleMenu}
                className="text-white dark:text-white light:text-black text-[18px] sm:text-[20px] leading-[40px] hover:text-[#FF640F] transition-all duration-300 ease-in-out"
              >
                About Us
              </Link>
              <Link
                href="/services"
                onClick={toggleMenu}
                className="text-white dark:text-white light:text-black text-[18px] sm:text-[20px] leading-[40px] hover:text-[#FF640F] transition-all duration-300 ease-in-out"
              >
                Services
              </Link>
              <Link
                href="/contact"
                onClick={toggleMenu}
                className="text-white dark:text-white light:text-black text-[18px] sm:text-[20px] leading-[40px] hover:text-[#FF640F] transition-all duration-300 ease-in-out"
              >
                Contact
              </Link>
              <Link
                href="/blog"
                onClick={toggleMenu}
                className="text-white dark:text-white light:text-black text-[18px] sm:text-[20px] leading-[40px] hover:text-[#FF640F] transition-all duration-300 ease-in-out"
              >
                Blog
              </Link>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
