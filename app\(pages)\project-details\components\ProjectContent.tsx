"use client"
import React from 'react'
import Image from 'next/image'
import { IoIosCheckmark } from "react-icons/io"

interface ProjectContentProps {
  projectDetails: any
}

export default function ProjectContent({ projectDetails }: ProjectContentProps) {
  // Find Process & Challenge step (step 02)
  const processStep = projectDetails.processSteps.find((step: any) => step.step === "02")
  // Find Summary step (step 03)
  const summaryStep = projectDetails.processSteps.find((step: any) => step.step === "03")

  return (
    <div className="w-full dark:text-white text-gray-800  py-8 md:py-12 lg:py-16 px-4 md:px-6 lg:px-8">
      <div className="max-w-full lg:max-w-[95%] mx-auto px-4 sm:px-6 lg:px-10">

        {/* Process & Challenge Section */}
        {processStep && (
          <div className="mb-12 md:mb-16 lg:mb-20">
            <div className="text-4xl sm:text-[42px] lg:text-[45px] font-bold leading-[1.2] sm:leading-[70px] tracking-[-1px] flex flex-col sm:flex-row items-start justify-start gap-4 sm:gap-8">
              <span className="text-4xl sm:text-5xl lg:text-[48px] font-bold dark:text-white text-gray-800 block sm:inline-block w-full sm:w-[10%]">
                {processStep.step}
              </span>
              <div className='w-full dark:text-white text-gray-800  sm:w-[90%] flex flex-col justify-between gap-4 sm:gap-6 items-start'>
                <span className='text-4xl sm:text-[42px] lg:text-[45px] font-bold leading-[1.2] sm:leading-[70px] tracking-[-1px]'>
                  {processStep.title}
                </span>

                <div className='flex flex-col gap-6 sm:gap-8'>
                  <div
                    className="text-base sm:text-lg lg:text-xl dark:text-white/70 text-gray-800  leading-relaxed sm:leading-[42px] prose prose-invert max-w-none [&>p]:mb-4 [&>a]:text-blue-400 [&>a]:underline [&>strong]:text-white [&>em]:text-white/80"
                    dangerouslySetInnerHTML={{ __html: processStep.description }}
                  />

                  {processStep.points && (
                    <ul className="space-y-3 sm:space-y-4">
                      {processStep.points.map((point: string, pointIndex: number) => (
                        <li key={pointIndex} className="flex items-start sm:items-center gap-3">
                          <div className="w-6 h-6 sm:w-8 sm:h-8 dark:text-white text-gray-800  rounded-full flex items-center justify-center flex-shrink-0 mt-1 sm:mt-0">
                            <IoIosCheckmark className="w-4 h-4 sm:w-6 sm:h-6 text-black" />
                          </div>
                          <span className="text-lg sm:text-xl lg:text-[22px] leading-normal sm:leading-[55px] dark:text-white text-gray-800 ">
                            {point}
                          </span>
                        </li>
                      ))}
                    </ul>
                  )}

                  <div
                    className="text-base sm:text-lg lg:text-xl dark:text-white/70 text-gray-800  leading-relaxed sm:leading-[42px] prose prose-invert max-w-none [&>p]:mb-4 [&>a]:text-blue-400 [&>a]:underline [&>strong]:text-white [&>em]:text-white/80"
                    dangerouslySetInnerHTML={{ __html: processStep.description2 }}
                  />
                </div>

                {/* Three Images Grid */}
                <div className="w-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mt-6">
                  {projectDetails.images.gallery.slice(0, 3).map((img: any, imgIndex: number) => (
                    <div
                      key={imgIndex}
                      className="relative h-48 sm:h-56 md:h-64 lg:h-[250px] rounded-xl sm:rounded-2xl lg:rounded-[24px] overflow-hidden bg-[#1a1a1a]"
                    >
                      <Image
                        src={img}
                        alt={`Process image ${imgIndex + 1}`}
                        width={400}
                        height={250}
                        className="object-cover w-full h-full"
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Summary Section */}
        {summaryStep && (
          <div className="mb-10 md:mb-14 lg:mb-16">
            <div className="text-4xl sm:text-[42px] lg:text-[45px] font-bold leading-[1.2] sm:leading-[70px] tracking-[-1px] flex flex-col sm:flex-row items-start justify-start gap-4 sm:gap-8">
              <span className="text-4xl sm:text-5xl lg:text-[48px] font-bold dark:text-white text-gray-800  block sm:inline-block w-full sm:w-[10%]">
                {summaryStep.step}
              </span>
              <div className='w-full sm:w-[90%] flex flex-col justify-between gap-4 sm:gap-6 items-start'>
                <span className='text-4xl sm:text-[42px] lg:text-[45px] font-bold leading-[1.2] sm:leading-[70px] tracking-[-1px]'>
                  {summaryStep.title}
                </span>

                <div>
                  <div
                    className="text-base sm:text-lg lg:text-xl dark:text-white/70 text-gray-800  leading-relaxed sm:leading-[40px] prose prose-invert max-w-none [&>p]:mb-4 [&>a]:text-blue-400 [&>a]:underline [&>strong]:text-white [&>em]:text-white/80"
                    dangerouslySetInnerHTML={{ __html: summaryStep.description }}
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}