"use client";
import Image from "next/image";
import profilechat from "@/public/new-assests/news-icons/heroicons/homeimage/profilechat.png";
import computercode from "@/public/new-assests/news-icons/heroicons/homeimage/computercode.png";    
import tabletpencil from "@/public/new-assests/news-icons/heroicons/homeimage/tabletpencil.png";    
import Link from "next/link";
export default function ContactUsHome() {
  return (
    <div className="text-white mt-10 flex flex-col items-center justify-center py-[32px] md:py-[48px] lg:py-[64px] px-[16px] md:px-[24px]">
      <div className="w-full max-w-[90%]">
        {/* Single Contact Card Container */}
        <div className="bg-[#2B2B2B] rounded-[32px] p-4 md:p-8 lg:p-10 relative overflow-hidden">
          {/* Center Content */}
          <div className="text-center">
            {/* Main Heading */}
            <h2 className="text-[40px] sm:text-[50px] md:text-[60px] lg:text-[80px] font-bold text-white mb-4 leading-[50px] sm:leading-[60px] md:leading-[70px] lg:leading-[80px] tracking-[-2px] sm:tracking-[-3px] lg:tracking-[-4px]">
              Want to Chat
              <Image
                src={profilechat}
                alt="Profile Chat"
                width={40}
                height={40}
                className="inline-block w-[50px] h-[40px] sm:w-[60px] sm:h-[50px] md:w-[70px] md:h-[60px] lg:w-[80px] lg:h-[60px] ml-1 sm:ml-2 mb-1 sm:mb-2"
              />
              ? Feel free to
              <br className="hidden sm:block" />
              Contact our Team.
            </h2>

            {/* Subtitle and Images */}
            <div className="flex flex-col lg:flex-row justify-between items-center gap-6 mt-8">
              {/* Tablet Pencil Image - shown on all screens but order changes */}
              <div className="order-1 lg:order-1 w-[120px] sm:w-[140px] md:w-[159.2px]">
                <Image
                  src={tabletpencil}
                  alt="Tablet Pencil"
                  className="w-full h-auto"
                  layout="responsive"
                />
              </div>

              {/* Text Content and Button */}
              <div className="order-3 lg:order-2 flex-1 max-w-[600px]">
                <p className="text-white/80 text-[18px] sm:text-[22px] md:text-[26px] lg:text-[28px] mb-6 sm:mb-8 leading-[22px] sm:leading-[26px] md:leading-[28px]">
                  If you have anything in mind just contact us with our expert.
                </p>

                {/* CTA Button */}
              <Link href="/contact">
                <button className="border border-white hover:bg-white hover:text-black text-white px-6 py-3 sm:px-8 sm:py-4 rounded-full text-[16px] sm:text-[18px] md:text-[20px] font-medium transition-all duration-300">
                  Let's Get Started
                </button>
              </Link>
              </div>

              {/* Computer Code Image - shown on all screens but order changes */}
              <div className="order-2 lg:order-3 w-[180px] sm:w-[220px] md:w-[261px]">
                <Image
                  src={computercode}
                  alt="Computer Code"
                  className="w-full h-auto"
                  layout="responsive"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}