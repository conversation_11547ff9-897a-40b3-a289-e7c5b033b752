"use client";

import { useState } from "react";
import { Service } from "@/api/services/services_api";
import { FaCheck } from "react-icons/fa6";

interface ServiceDetailsHeroProps {
  service: Service;
}

const getVideoForService = (service: Service): string => {
  return (
    service.videoUrl ||
    "/new-assests/news-icons/heroicons/services/newreduces.mp4"
  );
};

export default function ServiceDetailsHero({
  service,
}: ServiceDetailsHeroProps) {
  const [openAccordion, setOpenAccordion] = useState<number | null>(0);
  const videoSrc = getVideoForService(service);

  const toggleAccordion = (index: number) => {
    setOpenAccordion(openAccordion === index ? null : index);
  };

  return (
    <div className="space-y-8 md:space-y-12">
      {/* Main Title and Description */}
      <div className="space-y-2">
        <h1 className="text-4xl sm:text-5xl lg:text-[65px] xl:text-[72px] font-bold text-gray-800 dark:text-white leading-[1.2] lg:leading-[78px]">
          {service.title}
        </h1>
        <p className="text-base sm:text-lg lg:text-[20px] leading-relaxed lg:leading-[36px] text-gray-800 dark:text-white ">
          {service.description}
        </p>
      </div>

      {/* Service Video */}
      <div className="relative rounded-xl lg:rounded-2xl overflow-hidden bg-gray-800">
        <video
          className="w-full h-[250px] sm:h-[350px] md:h-[400px] object-cover"
          autoPlay
          loop
          muted
          playsInline
        >
          <source src={videoSrc} type="video/mp4" />
        </video>
      </div>

      {/* Strategy Section */}
      <div className="space-y-4 md:space-y-6">
        <h2 className="text-3xl md:text-4xl lg:text-[48px] font-bold text-gray-800 dark:text-white ">
          Our Strategy
        </h2>
        <div
          className="text-gray-800 dark:text-white  opacity-[70%] text-base md:text-lg lg:text-[20px] leading-relaxed lg:leading-[36px] prose prose-invert max-w-none [&>p]:mb-4 [&>a]:text-blue-400 [&>a]:underline [&>strong]:text-white [&>em]:text-white/80"
          dangerouslySetInnerHTML={{ __html: service.serviceDescription }}
        />

        {/* Strategy Points */}
        <div className="space-y-3 md:space-y-4">
          {service.importantPoints.map((point: string, index: number) => (
            <div
              key={index}
              className="flex items-start gap-4 md:gap-6 justify-start"
            >
              <div className="text-lg md:text-xl flex items-center justify-center text-gray-800 dark:text-white  text-center rounded-full mt-1">
                <FaCheck className="" />
              </div>
              <span className="text-gray-800 dark:text-white  text-base md:text-lg lg:text-[20px] leading-relaxed md:leading-[38px]">
                {point}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Before Contacting Section - Accordion */}
      <div className="w-full space-y-4 md:space-y-6">
        <h2 className="w-full lg:w-[70%] text-3xl md:text-4xl lg:text-[48px] leading-[1.2] lg:leading-[56px] font-bold text-gray-800 dark:text-white ">
          How to Get Started
        </h2>

        <div className="space-y-2 md:space-y-4 border-t py-2 md:py-3">
          {service.questionsAnswers.map((qa, index) => (
            <div key={qa._id} className="border-b border-[#FFFFFF33]">
              {/* Accordion Header */}
              <button
                onClick={() => toggleAccordion(index)}
                className="w-full flex items-center justify-between py-3 md:py-4 text-left hover:bg-gray-800/30 transition-colors rounded-lg px-2"
              >
                <div className="flex items-center space-x-2 md:space-x-4">
                  <span className="text-gray-800 dark:text-white  font-semibold text-lg md:text-xl lg:text-[24px] leading-[1.3] md:leading-[45px]">
                    {qa.order}. {qa.question}
                  </span>
                </div>
                <div
                  className={`transform transition-transform duration-300 ${
                    openAccordion === index ? "rotate-180" : ""
                  }`}
                >
                  <svg
                    className="w-5 h-5 text-gray-800 dark:text-white "
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </div>
              </button>

              {/* Accordion Content */}
              <div
                className={`overflow-hidden transition-all duration-300 ${
                  openAccordion === index
                    ? "max-h-96 opacity-100 pb-3 md:pb-4"
                    : "max-h-0 opacity-0"
                }`}
              >
                <div className="px-2">
                  <p className="dark:text-gray-300 text-gray-700 text-base md:text-lg lg:text-[20px] leading-relaxed lg:leading-[36px]">
                    {qa.answer}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
