"use client";

import React, { useState, useEffect } from 'react';
import {
  FiBriefcase,
  FiMapPin,
  FiClock,
  FiDollarSign,
  FiUsers,
  FiCalendar,
  FiFilter,
  FiSearch,
  FiRefreshCw
} from 'react-icons/fi';
import { getAllJobProfiles, JobProfile, isJobAcceptingApplications, formatJobDate } from '@/api/jobs/jobs_api';
import CareerApplicationModal from './components/CareerApplicationModal';
import { Toaster } from 'react-hot-toast';
import ScrollAnimationWrapper from "@/components/animations/ScrollAnimationWrapper";

const CareersPage: React.FC = () => {
  const [jobs, setJobs] = useState<JobProfile[]>([]);
  const [filteredJobs, setFilteredJobs] = useState<JobProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedJobType, setSelectedJobType] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('');
  const [showApplicationModal, setShowApplicationModal] = useState(false);
  const [selectedJob, setSelectedJob] = useState<JobProfile | null>(null);
  const [departments, setDepartments] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false); // State for filter toggle on mobile

  useEffect(() => {
    fetchJobs();
  }, []);

  useEffect(() => {
    filterJobs();
  }, [jobs, searchQuery, selectedJobType, selectedDepartment]);

  const fetchJobs = async () => {
    try {
      setLoading(true);
      const response = await getAllJobProfiles();
      setJobs(response.data);
      const uniqueDepartments = Array.from(new Set(response.data.map(job => job.department).filter((dept): dept is string => Boolean(dept))));
      setDepartments(uniqueDepartments);
    } catch (err) {
      console.error('Error fetching jobs:', err);
      setError('Failed to load job openings. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const filterJobs = () => {
    let filtered = jobs;
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(job =>
        job.jobTitle.toLowerCase().includes(query) ||
        job.jobDescription.toLowerCase().includes(query) ||
        job.techStack.some(tech => tech.toLowerCase().includes(query))
      );
    }
    if (selectedJobType) {
      filtered = filtered.filter(job => job.jobType === selectedJobType);
    }
    if (selectedDepartment) {
      filtered = filtered.filter(job => job.department === selectedDepartment);
    }
    setFilteredJobs(filtered);
  };

  const handleApply = (job: JobProfile) => {
    setSelectedJob(job);
    setShowApplicationModal(true);
  };

  const resetFilters = () => {
    setSearchQuery('');
    setSelectedJobType('');
    setSelectedDepartment('');
    setShowFilters(false); // Close filters on reset
  };

  const getJobTypeBadge = (jobType: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium";
    switch (jobType) {
      case 'full-time': return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'part-time': return `${baseClasses} bg-purple-100 text-purple-800`;
      case 'contract': return `${baseClasses} bg-yellow-100 text-yellow-800`;
      case 'internship': return `${baseClasses} bg-pink-100 text-pink-800`;
      case 'freelance': return `${baseClasses} bg-indigo-100 text-indigo-800`;
      default: return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <FiBriefcase className="mx-auto h-16 w-16 text-red-500 mb-4" />
          <h2 className="text-2xl font-bold mb-2 text-white">Error Loading Jobs</h2>
          <p className="text-gray-400">{error}</p>
          <button
            onClick={fetchJobs}
            className="mt-4 px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="text-white py-8 sm:py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <ScrollAnimationWrapper animation="fade-in-up">
          <div className="text-center mb-8 sm:mb-12">
            <div className="inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-orange-500 rounded-full mb-4 sm:mb-6">
              <FiBriefcase className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
            </div>
            <h1 className="text-3xl sm:text-4xl font-bold dark:text-white text-gray-800 mb-3 sm:mb-4">
              Join Our Team
            </h1>
            <p className="text-base sm:text-xl text-gray-400 max-w-3xl mx-auto">
              Discover exciting career opportunities and be part of our innovative team.
              We're always looking for talented individuals to help us build the future.
            </p>
          </div>
        </ScrollAnimationWrapper>

        {/* Filter Button for Mobile */}
        <div className="md:hidden mb-4">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center justify-center w-full px-4 py-3 bg-[#343434] text-white rounded-lg hover:bg-[#3b3b3b] transition-colors"
          >
            <FiFilter className="w-5 h-5 mr-2" />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </button>
        </div>

        {/* Filters */}
        <ScrollAnimationWrapper animation="fade-in-left" delay={200}>
          <div className={`bg-[#1c1c1c] rounded-lg p-4 sm:p-6 mb-6 sm:mb-8 ${showFilters ? 'block' : 'hidden md:block'}`}>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4">
              {/* Search */}
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 sm:w-5 sm:h-5" />
                <input
                  type="text"
                  placeholder="Search jobs..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-9 sm:pl-10 pr-3 sm:pr-4 py-2 sm:py-3 bg-[#343434] rounded-lg text-sm sm:text-base text-white placeholder-gray-400 outline-none"
                />
              </div>

              {/* Job Type Filter */}
              <select
                value={selectedJobType}
                onChange={(e) => setSelectedJobType(e.target.value)}
                className="px-3 sm:px-4 py-2 sm:py-3 bg-[#343434] rounded-lg text-sm sm:text-base text-white outline-none"
              >
                <option value="">All Job Types</option>
                <option value="full-time">Full-time</option>
                <option value="part-time">Part-time</option>
                <option value="contract">Contract</option>
                <option value="internship">Internship</option>
                <option value="freelance">Freelance</option>
              </select>

              {/* Department Filter */}
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="px-3 sm:px-4 py-2 sm:py-3 bg-[#343434] rounded-lg text-sm sm:text-base text-white outline-none"
              >
                <option value="">All Departments</option>
                {departments.map((department) => (
                  <option key={department} value={department}>
                    {department}
                  </option>
                ))}
              </select>

              {/* Reset Button */}
              <button
                onClick={resetFilters}
                className="flex items-center justify-center px-3 sm:px-4 py-2 sm:py-3 bg-[#343434] hover:bg-[#3b3b3b] text-white rounded-lg transition-colors text-sm sm:text-base"
              >
                <FiRefreshCw className="w-4 h-4 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                Reset
              </button>
            </div>
          </div>
        </ScrollAnimationWrapper>

        {/* Results Count */}
        <div className="mb-4 sm:mb-6">
          <p className="text-sm sm:text-base text-gray-400">
            {filteredJobs.length} job opening{filteredJobs.length !== 1 ? 's' : ''} available
          </p>
        </div>

        {/* Job Listings */}
        <ScrollAnimationWrapper animation="fade-in-up" delay={300}>
          {filteredJobs.length === 0 ? (
            <div className="text-center py-12 sm:py-16">
              <FiBriefcase className="mx-auto h-12 w-12 sm:h-16 sm:w-16 text-gray-600 mb-3 sm:mb-4" />
              <h3 className="text-lg sm:text-xl font-semibold text-white mb-2">No Job Openings Found</h3>
              <p className="text-sm sm:text-base text-gray-400 mb-4 sm:mb-6">
                {searchQuery || selectedJobType || selectedDepartment
                  ? "No jobs match your current filters. Try adjusting your search criteria."
                  : "We don't have any open positions at the moment. Check back soon for new opportunities!"
                }
              </p>
              {(searchQuery || selectedJobType || selectedDepartment) && (
                <button
                  onClick={resetFilters}
                  className="text-orange-500 hover:text-orange-400 transition-colors text-sm sm:text-base"
                >
                  Clear all filters
                </button>
              )}
            </div>
          ) : (
            <div className="grid gap-4 sm:gap-6">
              {filteredJobs.map((job) => (
                <div
                  key={job._id}
                  className="bg-[#1c1c1c] rounded-lg p-4 sm:p-6 transition-colors"
                >
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-3 sm:mb-4">
                        <div>
                          <h3 className="text-lg sm:text-xl font-semibold text-white mb-2">
                            {job.jobTitle}
                          </h3>
                          <div className="flex flex-wrap items-center gap-3 sm:gap-4 text-xs sm:text-sm text-gray-400 mb-2 sm:mb-3">
                            {job.department && (
                              <div className="flex items-center">
                                <FiBriefcase className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                                {job.department}
                              </div>
                            )}
                            {job.location && (
                              <div className="flex items-center">
                                <FiMapPin className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                                {job.location}
                              </div>
                            )}
                            {job.salaryRange && (
                              <div className="flex items-center">
                                <FiDollarSign className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                                {job.salaryRange}
                              </div>
                            )}
                            <div className="flex items-center">
                              <FiCalendar className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                              Apply by {formatJobDate(job.lastDayApplied)}
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-col items-end gap-1 sm:gap-2 min-w-16">
                          <span className={getJobTypeBadge(job.jobType)}>
                            {job.jobType}
                          </span>
                          {job.isUrgent && (
                            <span className="inline-flex items-center px-1 sm:px-2 py-0.5 sm:py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 whitespace-nowrap">
                              Urgent
                            </span>
                          )}
                        </div>
                      </div>

                      <p className="text-sm sm:text-base text-gray-300 mb-3 sm:mb-4 ">
                        {job.jobDescription}
                      </p>

                      {/* Tech Stack */}
                      <div className="mb-3 sm:mb-4">
                        <h4 className="text-xs sm:text-sm font-medium text-gray-400 mb-1 sm:mb-2">Required Skills:</h4>
                        <div className="flex flex-wrap gap-1 sm:gap-2">
                          {job.techStack.slice(0, 6).map((tech, index) => (
                            <span
                              key={index}
                              className="px-2 sm:px-3 py-0.5 sm:py-1 bg-[#343434] text-gray-300 rounded-full text-xs sm:text-sm"
                            >
                              {tech}
                            </span>
                          ))}
                          {job.techStack.length > 6 && (
                            <span className="px-2 sm:px-3 py-0.5 sm:py-1 bg-gray-700 text-gray-400 rounded-full text-xs sm:text-sm">
                              +{job.techStack.length - 6} more
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Experience */}
                      {job.experienceRequired && (
                        <div className="mb-3 sm:mb-4">
                          <span className="text-xs sm:text-sm text-gray-400">Experience: </span>
                          <span className="text-xs sm:text-sm text-white">{job.experienceRequired}</span>
                        </div>
                      )}
                    </div>

                    <div className="lg:ml-6 mt-3 sm:mt-4 lg:mt-0">
                      <button
                        onClick={() => handleApply(job)}
                        className="w-full lg:w-auto px-4 sm:px-6 py-2 sm:py-3 bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 focus:ring-offset-gray-900 text-sm sm:text-base"
                      >
                        Apply Now
                      </button>
                      {/* <div className="flex items-center justify-center lg:justify-start mt-2 text-xs sm:text-sm text-gray-400">
                        <FiUsers className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                        {job.applicationCount} applicant{job.applicationCount !== 1 ? 's' : ''}
                      </div> */}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollAnimationWrapper>

        {/* Career Application Modal */}
        {showApplicationModal && selectedJob && (
          <CareerApplicationModal
            job={selectedJob}
            isOpen={showApplicationModal}
            onClose={() => {
              setShowApplicationModal(false);
              setSelectedJob(null);
            }}
            onSuccess={() => {
              setShowApplicationModal(false);
              setSelectedJob(null);
              fetchJobs();
            }}
          />
        )}

        {/* Toast Notifications */}
        <Toaster />
      </div>
    </div>
  );
};

export default CareersPage;