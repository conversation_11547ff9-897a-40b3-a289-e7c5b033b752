"use client";

import React from 'react';
import { AnimatedSection } from './AnimatedSection';

interface ScrollAnimationWrapperProps {
  children: React.ReactNode;
  animation?: 'fade-in-up' | 'fade-in-left' | 'fade-in-right' | 'fade-in' | 'scale-in' | 'slide-in-bottom';
  delay?: number;
  className?: string;
  threshold?: number;
  rootMargin?: string;
  disabled?: boolean; // To disable animations for specific pages like blog/contact
}

const ScrollAnimationWrapper: React.FC<ScrollAnimationWrapperProps> = ({
  children,
  animation = 'fade-in-up',
  delay = 0,
  className = '',
  threshold = 0.1,
  rootMargin = '0px 0px -50px 0px',
  disabled = false,
}) => {
  // If animations are disabled, just return children without wrapper
  if (disabled) {
    return <>{children}</>;
  }

  return (
    <AnimatedSection
      animation={animation}
      delay={delay}
      className={className}
      threshold={threshold}
      rootMargin={rootMargin}
      triggerOnce={true}
    >
      {children}
    </AnimatedSection>
  );
};

export default ScrollAnimationWrapper;
