"use client";
import { useEffect, useRef, useState } from "react";

export default function StatsComponent() {
  const clientsRef = useRef<HTMLSpanElement>(null);
  const projectsRef = useRef<HTMLSpanElement>(null);
  const marketRef = useRef<HTMLSpanElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [hasAnimated, setHasAnimated] = useState(false);

  useEffect(() => {
    const animateCount = (
      ref: React.RefObject<HTMLSpanElement>,
      target: number,
      suffix: string = ""
    ) => {
      let current = 0;
      const increment = target / 50;
      const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
          current = target;
          clearInterval(timer);
        }
        if (ref.current) {
          ref.current.textContent =
            suffix === "+"
              ? `${Math.floor(current)}+`
              : suffix === "%"
              ? `${Math.floor(current)}%`
              : `${(current / 1000).toFixed(1)}K+`;
        }
      }, 20);
    };

    const startAnimation = () => {
      if (!hasAnimated) {
        animateCount(clientsRef, 1300);
        animateCount(projectsRef, 250, "+");
        animateCount(marketRef, 40, "%");
        setHasAnimated(true);
      }
    };

    // Intersection Observer to detect when component comes into view
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasAnimated) {
            startAnimation();
          }
        });
      },
      {
        threshold: 0.3, // Trigger when 30% of the component is visible
        rootMargin: "-50px", // Start animation 50px before the component is fully visible
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    // Cleanup observer on component unmount
    return () => {
      if (containerRef.current) {
        observer.unobserve(containerRef.current);
      }
    };
  }, [hasAnimated]);

  return (
    <div ref={containerRef} className="w-full  py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-[90%] mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-20 text-center">
          {/* Happy Clients */}
          <div className="p-6 rounded-lg ">
            <span
              ref={clientsRef}
              className="block text-[100px] font-bold dark:text-white text-black mb-2"
            >
              0
            </span>
            <h3 className="text-[24px] font-semibold dark:text-white text-black opacity-[60%]">
              Happy Clients
            </h3>
          </div>

          {/* Completed Projects */}
          <div className="p-6 rounded-lg">
            <span
              ref={projectsRef}
              className="block text-[100px] font-bold dark:text-white text-black mb-2"
            >
              0+
            </span>
            <h3 className="text-[24px] font-semibold dark:text-white text-black opacity-[60%]">
              Completed Project
            </h3>
          </div>

          {/* Cheap in the Market */}
          <div className="p-6 rounded-lg">
            <span
              ref={marketRef}
              className="block text-[100px] font-bold dark:text-white text-black mb-2"
            >
              0%
            </span>
            <h3 className="text-[24px] font-semibold dark:text-white text-black opacity-[60%]">
              Cheap in the Market
            </h3>
          </div>
        </div>
      </div>
    </div>
  );
}
