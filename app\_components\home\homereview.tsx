"use client";

import Image from "next/image";
import { <PERSON>, <PERSON>UpR<PERSON>, MousePointer2 } from "lucide-react";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi,
} from "@/components/ui/carousel";
import threelines from "@/public/new-assests/news-icons/heroicons/homeimage/threelines.svg";
import shukla from "@/public/new-assests/news-icons/heroicons/homeimage/review/1.png";
import harsul from "@/public/new-assests/news-icons/heroicons/homeimage/review/2.png";
import gautam from "@/public/new-assests/news-icons/heroicons/homeimage/review/3.png";
import girl from "@/public/new-assests/news-icons/heroicons/homeimage/review/girl.png";

// Review data
const reviews = [
  {
    id: 1,
    name: "<PERSON><PERSON><PERSON>",
    image: shukla,
    rating: 5,
    title: "Incredible work!",
    review:
      "Seamless experience! Improved our entire workflow—fast, reliable, and super easy to manage.",
    designation: "CEO <PERSON><PERSON> security system",
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON>",
    image: harsul,
    rating: 5,
    title: "Outstanding service!",
    review:
      "Delivered on time with excellent quality. Great support and truly professional from start.",
    designation: "Founder, Macgence AI",
  },
  {
    id: 3,
    name: "Varun Sharma",
    image: gautam,
    rating: 5,
    title: "Game changer!",
    review:
      "Smart features saved our time. Perfect tool for scaling business operations hassle-free.",
    designation: "Founder of Parv Events",
  },
  {
    id: 4,
    name: "Priya Sharma",
    image: girl,
    rating: 5,
    title: "Excellent results!",
    review:
      "Smooth setup, friendly team, and top-notch performance. We saw results from day one.",
    designation: "Founder of Parv Events",
  },
];

// Client profile images for header
const clientProfiles = [
  "/new-assests/news-icons/heroicons/homeimage/1.png",
  "/new-assests/news-icons/heroicons/homeimage/2.png",
  "/new-assests/news-icons/heroicons/homeimage/3.png",
  "/new-assests/news-icons/heroicons/homeimage/4.png",
];

export default function HomeReview() {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  const router = useRouter();

  const handleReviewClick = () => {
    router.push("/allreviewclient.tsx");
  };

  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  // Auto-rotate the carousel
  useEffect(() => {
    if (!api) return;

    const interval = setInterval(() => {
      api.scrollNext();
    }, 5000); // Change slide every 5 seconds

    return () => clearInterval(interval);
  }, [api]);

  const limitWords = (text: string, limit = 20) => {
    if (!text) return "";
    const words = text.trim().split(/\s+/);
    return (
      words.slice(0, limit).join(" ") + (words.length > limit ? "..." : "")
    );
  };

  return (
    <section className="py-8 sm:py-12 lg:py-16 bg-[#0C0C0C]">
      <div className="max-w-[1560px] mx-auto px-4 sm:px-6 lg:px-14">
        {/* Header Section */}
        <div className="flex flex-col md:flex-row gap-6 md:gap-8 mb-8 sm:mb-12 lg:mb-16 items-center justify-between">
          {/* Left - Big Number */}
          <div className="text-center md:text-left">
            <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-8xl font-bold text-white mb-2">
              13,000<span className="text-white">+</span>
            </h1>
            <p className="text-white text-base sm:text-lg md:text-xl">
              Customer love our product
            </p>
          </div>

          {/* Right - Client Satisfaction */}
          <div className="flex flex-col items-center md:items-start">
            <div className="flex flex-col items-center md:items-start gap-4 sm:gap-6">
              {/* Client Profile Images */}
              <div className="flex flex-row space-x-2 relative">
                {clientProfiles.map((profile, index) => (
                  <div
                    key={index}
                    className="relative w-10 h-10 sm:w-12 sm:h-12 rounded-full overflow-hidden border-2 sm:border-3 border-white"
                  >
                    <Image
                      src={profile}
                      alt={`Client ${index + 1}`}
                      fill
                      className="object-cover"
                    />
                  </div>
                ))}
                <div className="absolute -right-[6rem] sm:-right-[8rem] -top-8 sm:-top-10 z-0 hidden md:flex">
                  <Image
                    src={threelines}
                    alt="threelines"
                    width={24}
                    height={24}
                    className="w-[120px] sm:w-[155px] h-[80px] sm:h-[110px]"
                  />
                </div>
              </div>

              {/* Text with Arrow */}
              <p className="text-white text-base sm:text-lg md:text-xl font-medium leading-relaxed text-center md:text-left">
                Client satisfaction speaks louder than our words. Hear from
                them.
              </p>
            </div>
          </div>
        </div>

        {/* Carousel Section */}
        <div className="relative w-full max-w-full mx-auto">
          <Carousel
            setApi={setApi}
            opts={{
              align: "center",
              loop: true,
              containScroll: "keepSnaps",
            }}
            className="w-full"
          >
            <CarouselContent className="-ml-2 sm:-ml-4">
              {reviews.map((review) => (
                <CarouselItem
                  key={review.id}
                  className="pl-2 sm:pl-4 basis-[90%] sm:basis-[80%] md:basis-[70%] lg:basis-[60%]"
                >
                  <div
                    className="bg-[#2B2B2B] dark:bg-[#2B2B2B] light:bg-gray-100 backdrop-blur-sm cursor-pointer rounded-3xl p-4 sm:p-6 md:p-8 lg:p-10 mx-1 sm:mx-2 relative group hover:bg-[#333333] dark:hover:bg-[#333333] light:hover:bg-gray-200 transition-all duration-300 hover:scale-[1.02] hover:shadow-xl border border-transparent hover:border-[#19CC35]/20 min-h-[500px] flex flex-col"
                    onClick={handleReviewClick}
                  >
                    {/* Click Indicator - Top Right */}
                    <div className="absolute top-3 right-3 sm:top-4 sm:right-4 md:top-6 md:right-6 flex items-center justify-center gap-2 w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full bg-[#737373] text-white/60 group-hover:text-[#FF640F] transition-all duration-300 group-hover:scale-110">
                      <ArrowUpRight className="w-5 h-5 sm:w-6 sm:h-6 md:w-8 md:h-8" />
                    </div>

                    {/* Mobile Click Indicator */}
                    <div className="absolute top-3 right-3 sm:top-4 sm:right-4 md:hidden flex items-center text-white/60 group-hover:text-[#19CC35] transition-all duration-300 group-hover:scale-110">
                      <MousePointer2 className="w-4 h-4 sm:w-5 sm:h-5 animate-pulse hover:animate-bounce hover:duration-[5000ms] transition-all" />
                    </div>

                    <div className="flex flex-col md:flex-row items-center gap-4 sm:gap-6 md:gap-8 w-full flex-1">
                      {/* Left - Profile Image */}
                      <div className="flex items-center justify-center w-full md:w-[50%]">
                        <div className="w-[200px] h-[200px] sm:w-[300px] sm:h-[300px] md:w-[350px] md:h-[350px] lg:w-[415px] lg:h-[420px] bg-white rounded-tl-[150px] sm:rounded-tl-[200px] md:rounded-tl-[300px] lg:rounded-tl-[350px] rounded-tr-[150px] sm:rounded-tr-[200px] md:rounded-tr-[300px] lg:rounded-tr-[350px] flex items-center justify-center overflow-hidden">
                          <Image
                            src={review.image}
                            alt={review.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </div>

                      {/* Right - Content */}
                      <div className="flex flex-col justify-between items-start h-full w-full md:w-[50%] flex-1">
                        <div className="w-full">
                          <h3 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold underline decoration-[#19CC35] text-white mb-2 sm:mb-3 md:mb-4 transition-colors duration-300">
                            {review.title}
                          </h3>
                          <div className="min-h-[120px]">
                            <p className="text-white text-sm sm:text-base md:text-lg lg:text-2xl leading-relaxed mb-3 sm:mb-4 md:mb-6">
                              "{limitWords(review.review)}"
                            </p>
                          </div>
                        </div>

                        <div className="flex xs:flex-col flex-row md:flex-col gap-3 w-full">
                          {/* Name and Designation */}
                          <div className="flex flex-col lg:flex-row items-center gap-2 sm:gap-3">
                            <h4 className="text-white font-bold text-sm sm:text-base md:text-lg">
                              {review.name},
                            </h4>
                            <p className="text-white opacity-[40%] font-normal text-sm sm:text-base md:text-lg">
                              {review.designation}
                            </p>
                          </div>

                          {/* Rating */}
                          <div className="flex">
                            {[...Array(review.rating)].map((_, i) => (
                              <Star
                                key={i}
                                className="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 fill-[#f8da33] text-[#f8da33] border-none"
                              />
                            ))}
                            <span className="text-[#ffffff] ml-2 font-semibold text-xs sm:text-sm md:text-base">
                              {review.rating}.00
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>

          {/* Carousel Dots */}
          <div className="flex justify-center gap-2 mt-6 sm:mt-8">
            {Array.from({ length: count }).map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full transition-all duration-300 ${
                  index === current - 1
                    ? "bg-white w-6 sm:w-8"
                    : "bg-[#222222] hover:bg-gray-500"
                }`}
                onClick={() => api?.scrollTo(index)}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
