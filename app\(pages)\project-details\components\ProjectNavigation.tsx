"use client"
import React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { BsArrowRight, BsArrowLeft } from "react-icons/bs";

interface ProjectNavigationProps {
  nextProject: {
    id: string
    title: string
    slug: string
  } | null
  previousProject: {
    id: string
    title: string
    slug: string
  } | null
}

export default function ProjectNavigation({ nextProject, previousProject }: ProjectNavigationProps) {
  // Don't render if no projects available
  if (!nextProject && !previousProject) {
    return null
  }

  return (
    <div className="w-full dark:text-white text-gray-800  py-6 md:py-8 lg:py-10 px-4 md:px-6">
      <div className="max-w-full lg:max-w-[95%] mx-auto px-4 sm:px-6 lg:px-10 pt-8 border-t dark:border-white/10 border-black/50">

        {/* Navigation Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 md:gap-8">

          {/* Previous Project */}
          {previousProject && (
            <Link href={`/project-details/${previousProject.id}/${previousProject.slug}`}>
              <div className="flex flex-row items-center gap-4 sm:gap-6 rounded-lg sm:rounded-xl lg:rounded-[24px] p-4 sm:p-6 md:p-8 transition-all duration-300 hover:scale-[1.02] group cursor-pointer">
                <div className="w-12 h-12 sm:w-16 sm:h-16 lg:w-[75px] lg:h-[75px] text-xl sm:text-2xl lg:text-[28px] border dark:border-white border-gray-800 rounded-full flex items-center justify-center group-hover:text-[#FF640F] group-hover:border-[#FF640F] transition-colors flex-shrink-0">
                  <BsArrowLeft />
                </div>

                <div className="flex-1 min-w-0">
                  <span className="text-xs sm:text-sm md:text-[16px] dark:text-white/60 text-gray-800  uppercase tracking-wider">
                    Previous
                  </span>
                  <h3 className="text-base sm:text-lg md:text-xl lg:text-2xl font-bold dark:text-white text-gray-800  group-hover:text-[#FF640F] transition-colors truncate">
                    {previousProject.title}
                  </h3>
                </div>
              </div>
            </Link>
          )}

          {/* Next Project */}
          {nextProject && (
            <Link href={`/project-details/${nextProject.id}/${nextProject.slug}`}>
              <div className="flex flex-row items-center justify-end gap-4 sm:gap-6 rounded-lg sm:rounded-xl lg:rounded-[24px] p-4 sm:p-6 md:p-8 transition-all duration-300 hover:scale-[1.02] group cursor-pointer">
                <div className="flex-1 min-w-0 text-right">
                  <span className="text-xs sm:text-sm md:text-[16px] dark:text-white/60 text-gray-800  uppercase tracking-wider">
                    Next
                  </span>
                  <h3 className="text-base sm:text-lg md:text-xl lg:text-2xl font-bold dark:text-white text-gray-800  group-hover:text-[#FF640F] transition-colors truncate">
                    {nextProject.title}
                  </h3>
                </div>

                <div className="w-12 h-12 sm:w-16 sm:h-16 lg:w-[75px] lg:h-[75px] text-xl sm:text-2xl lg:text-[28px] border dark:border-white border-gray-800  rounded-full flex items-center justify-center group-hover:text-[#FF640F] group-hover:border-[#FF640F] transition-colors flex-shrink-0">
                  <BsArrowRight />
                </div>
              </div>
            </Link>
          )}
        </div>

        {/* Back to Projects (commented out but made responsive) */}
        {/* <div className="text-center mt-8 sm:mt-10 md:mt-12 lg:mt-16">
          <Link href="/projects">
            <button className="border border-white text-white text-base sm:text-lg md:text-xl px-6 sm:px-8 py-3 sm:py-4 rounded-full font-medium hover:bg-white hover:text-black transition-colors">
              View All Projects
            </button>
          </Link>
        </div> */}
      </div>
    </div>
  )
}