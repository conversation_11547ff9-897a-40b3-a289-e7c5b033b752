"use client";
import React, { useEffect, useRef, useState } from "react";
import { motion, useInView, useMotionValue, useSpring } from "framer-motion";

interface CounterData {
  number: number;
  label: string;
  suffix?: string;
}

interface AnimatedCounterProps {
  counters?: CounterData[];
  className?: string;
}

const defaultCounters: CounterData[] = [
  { number: 150, label: "Projects Completed", suffix: "+" },
  { number: 98, label: "Client Satisfaction", suffix: "%" },
  { number: 50, label: "Team Members", suffix: "+" }
];

const AnimatedNumber: React.FC<{ 
  value: number; 
  suffix?: string; 
  inView: boolean;
}> = ({ value, suffix = "", inView }) => {
  const motionValue = useMotionValue(0);
  const springValue = useSpring(motionValue, {
    damping: 60,
    stiffness: 100,
  });
  const [displayValue, setDisplayValue] = useState(0);

  useEffect(() => {
    if (inView) {
      motionValue.set(value);
    }
  }, [motionValue, value, inView]);

  useEffect(() => {
    return springValue.on("change", (latest) => {
      setDisplayValue(Math.round(latest));
    });
  }, [springValue]);

  return (
    <span className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-white">
      {displayValue}{suffix}
    </span>
  );
};

const AboutNumberComponent: React.FC<AnimatedCounterProps> = ({
  counters = defaultCounters,
  className = ""
}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: "-100px"
  });

  return (
    <div 
      ref={ref}
      className={`w-full py-16 md:py-20 lg:py-24 ${className}`}
    >
      <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="text-center mb-12 md:mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">
            Our <span className="text-orange-500">Achievements</span>
          </h2>
          <p className="text-gray-400 text-lg md:text-xl max-w-2xl mx-auto">
            Numbers that speak for our commitment to excellence
          </p>
        </motion.div>

        {/* Counter Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12">
          {counters.map((counter, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50, scale: 0.8 }}
              animate={isInView ? { 
                opacity: 1, 
                y: 0, 
                scale: 1 
              } : { 
                opacity: 0, 
                y: 50, 
                scale: 0.8 
              }}
              transition={{ 
                duration: 0.6, 
                delay: index * 0.2,
                ease: "easeOut"
              }}
              className="text-center group"
            >
              {/* Number Container */}
              <div className="relative mb-4">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.2 }}
                  className="inline-block p-6 md:p-8 rounded-2xl bg-gradient-to-br from-gray-800/50 to-gray-900/50 border border-gray-700 group-hover:border-orange-500/50 transition-all duration-300"
                >
                  <AnimatedNumber 
                    value={counter.number} 
                    suffix={counter.suffix}
                    inView={isInView}
                  />
                </motion.div>
                
                {/* Glow Effect */}
                <div className="absolute inset-0 rounded-2xl bg-orange-500/10 blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
              </div>

              {/* Label */}
              <motion.h3
                initial={{ opacity: 0 }}
                animate={isInView ? { opacity: 1 } : { opacity: 0 }}
                transition={{ duration: 0.4, delay: index * 0.2 + 0.3 }}
                className="text-xl md:text-2xl font-medium text-gray-300 group-hover:text-white transition-colors duration-300"
              >
                {counter.label}
              </motion.h3>
            </motion.div>
          ))}
        </div>

        {/* Decorative Elements */}
        <motion.div
          initial={{ opacity: 0, scale: 0 }}
          animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="flex justify-center mt-12 md:mt-16"
        >
          <div className="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 rounded-full" />
        </motion.div>
      </div>
    </div>
  );
};

export default AboutNumberComponent;
