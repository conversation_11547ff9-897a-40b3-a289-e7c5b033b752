"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import { BsArrowRight } from "react-icons/bs";
import Link from "next/link";
import { getAllProjects, transformProjectToCard, ProjectCardData } from "@/api/projects/projects_api";

export const ProjectCards = () => {
  const [projects, setProjects] = useState<ProjectCardData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        const projectsData = await getAllProjects();
        const transformedProjects = projectsData.map(transformProjectToCard);
        setProjects(transformedProjects);
        setError(null);
      } catch (err: any) {
        setError(err.message || "Failed to load projects");
        console.error("Error loading projects:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  if (loading) {
    return (
      <div className="w-full text-white py-8 px-4 flex items-center justify-center min-h-[50vh]">
        <div className="text-center">
          <div className="text-xl md:text-2xl font-bold">Loading Projects...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full text-white py-8 px-4 flex items-center justify-center min-h-[50vh]">
        <div className="text-center">
          <div className="text-xl md:text-2xl font-bold text-red-500">Error: {error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full dark:text-white text-gray-800 py-8 px-4 sm:px-6 md:py-12 lg:py-16 flex items-center justify-center">
      <div className="w-full max-w-[1400px] px-4 sm:px-6">
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 md:mb-12 lg:mb-16 gap-4">
          <h2 className="w-full sm:w-1/2 text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold leading-tight tracking-[-1px]">
            Our Projects
          </h2>
          <p className="w-full sm:w-1/2 text-base sm:text-lg md:text-xl leading-relaxed">
            AGKraft provides flexible digital solutions packed with features that suit every brand’s growth journey
          </p>
        </div>

        {/* Projects Grid */}
        <div className="space-y-8 md:space-y-10 lg:space-y-12">
          {projects.map((project) => (
            <div
              key={project.id}
              className="flex flex-col lg:flex-row gap-6 md:gap-8 lg:gap-12 items-center w-full"
            >
              {/* Left Content Card */}
              <Link href={`/project-details/${project.id}/${project.slug}`} className="w-full lg:w-3/5">
                <div
                  className="bg-[#2B2B2B] hover:bg-[#363636] group rounded-2xl cursor-pointer p-10 sm:p-10 h-[280px] sm:h-[320px] md:h-[360px] lg:h-[400px] flex flex-col sm:flex-row justify-between gap-4 sm:gap-6 relative overflow-hidden transition-all duration-300 hover:scale-[1.02]"
                >
                  {/* Project Serial Number */}
                  <div className="text-lg sm:text-xl md:text-2xl font-bold group-hover:text-[#FF640F] text-white/80">
                    {project.serialNo.toString().padStart(2, "0")}
                  </div>

                  {/* Project Title and Description */}
                  <div className=" flex flex-col justify-between">
                    <div>
                      <h3 className="text-white/20 text-sm sm:text-base md:text-lg uppercase">
                        {project.category}
                      </h3>
                      <h3 className="text-xl sm:text-2xl md:text-3xl lg:text-5xl font-bold text-white leading-tight mb-2">
                        {project.title}
                      </h3>
                    </div>
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                      <p className="text-sm sm:text-base md:text-lg text-white/90 leading-relaxed max-w-full sm:max-w-[70%]">
                        {project.description.split(" ").slice(0, 12).join(" ")}
                        {project.description.split(" ").length > 12 ? "..." : ""}
                      </p>
                      <div className="w-12 h-12 sm:w-16 sm:h-16 border text-white border-white group-hover:border-[#FF640F] group-hover:text-[#FF640F] text-2xl sm:text-3xl rounded-full flex items-center justify-center backdrop-blur-sm transition-all duration-300">
                        <BsArrowRight />
                      </div>
                    </div>
                  </div>
                </div>
              </Link>

              {/* Right Image */}
              <div className="relative h-[280px] sm:h-[320px] md:h-[360px] lg:h-[400px] rounded-2xl overflow-hidden w-full lg:w-2/5">
                <Image
                  src={project.bigImageUrl}
                  alt={`${project.title} Image`}
                  fill
                  className="object-cover w-full h-full"
                  sizes="(max-width: 640px) 100vw, (max-width: 1024px) 60vw, 40vw"
                  priority={project.serialNo === 1} // Priority for first project image
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};