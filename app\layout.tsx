import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import <PERSON>ript from "next/script";
import { FaWhatsapp } from "react-icons/fa";
import Chatbot from "../app/_components/chatbot"; // Import the Client Component
import { ThemeProvider } from "./_components/providers/ThemeProvider";

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-poppins",
  fallback: ["sans-serif"],
});

export const metadata: Metadata = {
  metadataBase: new URL("https://agkraft.co"), // Add this line
  title: "AGKRAFT - Software Development | Web & Mobile App Experts",
  description:
    "AGKRAFT is a leading tech partner offering AI integration, blockchain development, SaaS platforms, and custom software solutions. We build scalable web and mobile apps that drive innovation for startups and enterprises.",
  keywords: [
    "software development company",
    "web development services",
    "mobile app development",
    "eCommerce development",
    "custom software solutions",
    "Android app development",
    "iOS app development",
    "responsive website design",
    "business technology solutions",
    "AI integration services",
    "AI development company",
    "machine learning solutions",
    "blockchain development company",
    "blockchain platform development",
    "smart contract development",
    "Web3 development services",
    "SaaS platform development",
    "SaaS product development",
    "custom SaaS applications",
    "AGKRAFT software services",
    "best technology partner",
    "top mobile app developers India",
    "website design company in India",
    "enterprise software development",
    "full stack development services",
  ],
  authors: [{ name: "Kunal Verma" }, { name: "Gulshan" }, { name: "Aryan" }],
  robots: "index, follow",
  openGraph: {
    title: "AGKRAFT – Software & App Development Company",
    description:
      "Professional services in software, web design, mobile applications, AI integration and development, blockchain development platform, SaaS platform and eCommerce solutions.",
    images: ["/logo.png"],
    url: "https://agkraft.co",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "AGKRAFT – Your Tech Partner",
    description:
      "Web & mobile app development experts for custom, scalable digital products.",
    images: ["/logo.png"],
  },
};

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" className={poppins.variable}>
      <body className="bg-white dark:bg-[#0B0A0A] text-black dark:text-white transition-colors duration-300" suppressHydrationWarning>
        <ThemeProvider>
        {/* Google Tag Manager (noscript) */}
        <noscript>
          <iframe
            src="https://www.googletagmanager.com/ns.html?id=GTM-PL9XL4JJ"
            height="0"
            width="0"
            style={{ display: "none", visibility: "hidden" }}
          ></iframe>
        </noscript>

        {children}

        {/* Chatbot Component */}
        <Chatbot />

        {/* GTM Script */}
        <Script
          id="gtm-script"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
              new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
              j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
              'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
              })(window,document,'script','dataLayer','GTM-PL9XL4JJ');
            `,
          }}
        />

        {/* Google Ads */}
        <Script
          async
          src="https://www.googletagmanager.com/gtag/js?id=AW-16870433416"
        />
        <Script
          id="google-ads"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'AW-16870433416');
            `,
          }}
        />

        {/* Google Ads Conversion Tracking */}
        <Script
          id="google-conversion"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              gtag('event', 'conversion', {
                'send_to': 'AW-16870433416/tpjqCIXD_KAaEIjFuew-'
              });
            `,
          }}
        />

        {/* JSON-LD Structured Data */}
        <Script
          id="structured-data"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              name: "AGKRAFT",
              url: "https://agkraft.co/",
              logo: "/logo.png",
              description:
                "AGKRAFT is a full-service tech company offering AI, blockchain, SaaS, mobile, and web development solutions.",
              sameAs: [
                "https://www.linkedin.com/company/agkraft",
                "https://www.instagram.com/agkraft",
                "https://www.facebook.com/agkraft",
                "https://twitter.com/agkraft",
              ],
              contactPoint: [
                {
                  "@type": "ContactPoint",
                  telephone: "+91-8383049814, 9911572491, 9262975957",
                  contactType: "Customer Service",
                },
              ],
              makesOffer: [
                {
                  "@type": "Service",
                  name: "AI Integration & Development",
                  description:
                    "Custom AI and machine learning development, chatbot solutions, and automation systems.",
                },
                {
                  "@type": "Service",
                  name: "Blockchain Development",
                  description:
                    "Smart contracts, Web3 platforms, token development, and blockchain integrations.",
                },
                {
                  "@type": "Service",
                  name: "SaaS Platform Development",
                  description:
                    "SaaS product design, development, cloud deployment, and multi-tenant support.",
                },
                {
                  "@type": "Service",
                  name: "Mobile App Development",
                  description:
                    "Native and cross-platform apps for iOS and Android built with performance and UX in mind.",
                },
                {
                  "@type": "Service",
                  name: "Web Development",
                  description:
                    "Custom websites and portals with modern technologies, optimized for speed and SEO.",
                },
                {
                  "@type": "Service",
                  name: "eCommerce Development",
                  description:
                    "High-converting eCommerce websites with inventory, payment, and cart integration.",
                },
              ],
            }),
          }}
        />

          {/* WhatsApp Icon Button */}
          <a
            href="https://wa.me/919911572491"
            target="_blank"
            rel="noopener noreferrer"
            className="fixed bottom-24 right-5 z-50 bg-green-500 hover:bg-green-600 text-white p-3 rounded-full shadow-lg transition-all duration-300"
            aria-label="Chat on WhatsApp"
          >
            <FaWhatsapp className="w-6 h-6 text-2xl" />
          </a>
        </ThemeProvider>
      </body>
    </html>
  );
}