"use client";
import { useState, useEffect } from "react";
import doublemark from "@/public/new-assests/news-icons/heroicons/doubleaxlimationmark.svg";
import Image from "next/image";
import { IoIosArrowRoundForward } from "react-icons/io";
import { IoIosArrowRoundBack } from "react-icons/io";
import imgboy1 from "@/public/new-assests/news-icons/heroicons/homeimage/review/1.png";
import imgboy2 from "@/public/new-assests/news-icons/heroicons/homeimage/review/2.png";
import imgboy3 from "@/public/new-assests/news-icons/heroicons/homeimage/review/3.png";
import imgboy4 from "@/public/new-assests/news-icons/heroicons/homeimage/review/5.png";
import imgboy6 from "@/public/new-assests/news-icons/heroicons/homeimage/review/6.png";
import imgboy7 from "@/public/new-assests/news-icons/heroicons/homeimage/review/8.png";
import imgboy5 from "@/public/new-assests/news-icons/heroicons/homeimage/review/girl.png";
import imggirl1 from "@/public/new-assests/news-icons/heroicons/homeimage/review/girl.png";
import imggirl2 from "@/public/new-assests/news-icons/heroicons/homeimage/review/girl2.png";
import imggirl02 from "@/public/new-assests/news-icons/heroicons/homeimage/review/girl02.png";
import imggirl3 from "@/public/new-assests/news-icons/heroicons/homeimage/review/girl3.png";
import imggirl4 from "@/public/new-assests/news-icons/heroicons/homeimage/review/girl4.png";
import imggirl5 from "@/public/new-assests/news-icons/heroicons/homeimage/review/girl5.png";
import imggirl6 from "@/public/new-assests/news-icons/heroicons/homeimage/review/girl02.png";

export default function ThirdSliderCards() {
  const [currentSlide, setCurrentSlide] = useState(6);
  const [windowWidth, setWindowWidth] = useState(0);
  const [cardWidth, setCardWidth] = useState(402);
  const [cardsToShow, setCardsToShow] = useState(1);

  const testimonials = [
    {
      quote: "AGKraft delivered our project on time with perfect execution.",
      author: "Ravi Sharma",
      location: "Delhi, India",
      image: imgboy1,
    },
    {
      quote:
        "Very professional, responsive, and reliable team. Highly recommend AGKraft.",
      author: "Neha Kapoor",
      location: "Mumbai, India",
      image: imggirl1,
    },
    {
      quote: "Our website looks amazing and performs fast. Great job AGKraft!",
      author: "Amit Verma",
      location: "Lucknow, India",
      image: imgboy2,
    },
    {
      quote:
        "Designs are clean, fast-loading, and user-friendly. Thanks AGKraft!",
      author: "Pooja Mehta",
      location: "Bangalore, India",
      image: imggirl2,
    },
    {
      quote:
        "AGKraft understood our needs and executed everything beyond expectations.",
      author: "Karan Patel",
      location: "Ahmedabad, India",
      image: imgboy3,
    },
    {
      quote:
        "Loved the process and support throughout the website development journey.",
      author: "Sneha Reddy",
      location: "Hyderabad, India",
      image: imggirl3,
    },
    {
      quote:
        "Smart solutions, creative designs, and always available for support.",
      author: "Anil Kumar",
      location: "Chennai, India",
      image: imgboy4,
    },
    {
      quote:
        "Thanks to AGKraft for bringing our vision to life Great work with agkraft.",
      author: "Divya Jain",
      location: "Pune, India",
      image: imggirl5,
    },
    {
      quote: "The AGKraft team is super skilled and easy to work with.",
      author: "Rajeev Bansal",
      location: "Jaipur, India",
      image: imgboy5,
    },
    {
      quote:
        "Our website traffic improved noticeably after AGKraft's redesign and SEO.",
      author: "Isha Gupta",
      location: "Noida, India",
      image: imggirl6,
    },
    {
      quote:
        "They handled everything from design to deployment. Truly worry-free service.",
      author: "Mohit Thakur",
      location: "Chandigarh, India",
      image: imgboy6,
    },
    {
      quote: "AGKraft gave us a sleek app with excellent user experience.",
      author: "Tanvi Desai",
      location: "Surat, India",
      image: imggirl4,
    },
  ];

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);

      if (window.innerWidth < 640) {
        // Mobile
        setCardWidth(300);
        setCardsToShow(1);
      } else if (window.innerWidth < 768) {
        // Small tablet
        setCardWidth(350);
        setCardsToShow(1);
      } else if (window.innerWidth < 1024) {
        // Tablet
        setCardWidth(380);
        setCardsToShow(1);
      } else {
        // Desktop
        setCardWidth(402);
        setCardsToShow(1);
      }
    };

    // Set initial width
    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const nextSlide = () => {
    setCurrentSlide((prev) => prev + cardsToShow);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => Math.max(0, prev - cardsToShow));
  };

  return (
    <div className="w-full mt-10 dark:text-white text-gray-800 px-4 sm:px-6 lg:px-8">
      <div className="max-w-full">
        <div className="flex flex-col lg:flex-row items-center w-full justify-center">
          {/* Left side content */}
          <div className="flex w-full lg:w-[40%] justify-center lg:justify-start lg:px-[5rem] items-center lg:items-start flex-col text-center lg:text-left mb-10 lg:mb-0">
            <div className="mb-4 lg:mb-6">
              <span className="text-[#FF640F] text-[14px] sm:text-[16px] lg:text-[18px] leading-[32px] font-medium tracking-[6%] uppercase">
                TESTIMONIALS
              </span>
            </div>
            <h2 className="text-[36px] sm:text-[48px] lg:text-[60px] xl:text-[72px] font-bold leading-[40px] sm:leading-[56px] lg:leading-[72px] tracking-[-1px] sm:tracking-[-2px] mb-6 lg:mb-12">
              1.2k+ Clients
              <br />
              Love us
            </h2>

            {/* Navigation arrows */}
            <div className="flex space-x-4">
              <button
                onClick={prevSlide}
                className="w-[60px] h-[60px] sm:w-[80px] sm:h-[80px] lg:w-[100px] lg:h-[100px] hover:bg-[#f77234] dark:text-white text-gray-800 hover:border-[#f77234] hover:text-white rounded-full flex items-center justify-center border dark:border-white border-gray-800 transition-colors duration-200"
              >
                <IoIosArrowRoundBack className="text-[30px] sm:text-[40px] lg:text-[50px]" />
              </button>
              <button
                onClick={nextSlide}
                className="w-[60px] h-[60px] sm:w-[80px] sm:h-[80px] lg:w-[100px] lg:h-[100px] border hover:bg-[#f77234] dark:border-white border-gray-800 hover:border-[#f77234] dark:text-white text-gray-800 rounded-full flex items-center justify-center transition-colors duration-200"
              >
                <IoIosArrowRoundForward className="text-[30px] sm:text-[40px] lg:text-[50px]" />
              </button>
            </div>
          </div>

          {/* Right side testimonial cards */}
          <div className="flex w-full lg:w-[60%] relative overflow-hidden">
            {/* Sliding container */}
            <div
              className="flex transition-transform duration-700 ease-in-out"
              style={{
                transform: `translateX(-${currentSlide * (cardWidth + 32)}px)`, // cardWidth + gap
              }}
            >
              {/* Create very large array for truly infinite loop */}
              {Array.from({ length: testimonials.length * 10 }, (_, index) => {
                const testimonial = testimonials[index % testimonials.length];

                return (
                  <div
                    key={index}
                    className="flex-shrink-0 mx-4 sm:mx-8 transition-all duration-700 ease-in-out hover:scale-105"
                    style={{
                      width: `${cardWidth}px`,
                    }}
                  >
                    <div className="dark:bg-[#504e4e66] bg-[white] text-gray-800 border border-zinc-800 dark:text-white hover:bg-[#FFFFFF33] cursor-pointer rounded-2xl p-6 sm:p-8 h-[26rem] sm:h-[30rem] flex flex-col justify-between">
                      {/* Quote marks */}
                      <div className="mb-4 sm:mb-6 ">
                        <Image
                          src={doublemark}
                          alt="Quote marks"
                          width={32}
                          height={24}
                          className="w-[24px] h-[16px] sm:w-[28px] sm:h-[18px] dark:bg-transparent bg-gray-800"
                        />
                      </div>

                      {/* Testimonial text */}
                      <p className="text-gray-800 dark:text-white text-[20px] sm:text-[24px] lg:text-[28px] leading-[32px] sm:leading-[36px] lg:leading-[40px] mb-6 sm:mb-8 flex-grow">
                        {testimonial.quote}
                      </p>

                      {/* Author info */}
                      <div className="flex items-center">
                        <Image
                          src={testimonial.image}
                          alt={testimonial.author}
                          className="rounded-full mr-4 bg-white w-[50px] h-[50px] sm:w-[65px] sm:h-[65px]"
                        />
                        <div>
                          <div className="dark:text-white  text-gray-800 font-semibold text-[18px] sm:text-[20px] lg:text-[24px] leading-[24px] sm:leading-[28px] lg:leading-[30px] tracking-[-0.3px]">
                            {testimonial.author}
                          </div>
                          <div className="dark:text-gray-400 text-gray-600 text-[16px] sm:text-[18px] lg:text-[20px] leading-[24px] sm:leading-[28px] lg:leading-[30px]">
                            {testimonial.location}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
