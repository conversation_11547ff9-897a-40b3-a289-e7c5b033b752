Stack trace:
Frame         Function      Args
0007FFFF9880  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFF9880, 0007FFFF8780) msys-2.0.dll+0x1FEBA
0007FFFF9880  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9B58) msys-2.0.dll+0x67F9
0007FFFF9880  000210046832 (000210285FF9, 0007FFFF9738, 0007FFFF9880, 000000000000) msys-2.0.dll+0x6832
0007FFFF9880  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9880  0002100690B4 (0007FFFF9890, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9B60  00021006A49D (0007FFFF9890, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD14C80000 ntdll.dll
7FFD13C40000 KERNEL32.DLL
7FFD121B0000 KERNELBASE.dll
7FFD0B790000 apphelp.dll
7FFD14A60000 USER32.dll
7FFD12920000 win32u.dll
7FFD148D0000 GDI32.dll
7FFD12660000 gdi32full.dll
7FFD125B0000 msvcp_win.dll
7FFD11DB0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD12FB0000 advapi32.dll
7FFD12B00000 msvcrt.dll
7FFD14900000 sechost.dll
7FFD14110000 RPCRT4.dll
7FFD113A0000 CRYPTBASE.DLL
7FFD11F00000 bcryptPrimitives.dll
7FFD13AC0000 IMM32.DLL
