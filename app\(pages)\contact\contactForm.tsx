import React, { useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { submitContactForm, ContactFormData } from "@/api/contact/contact_api";

const ContactForm = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    countryCode: "+91",
    phoneNumber: "",
    service: "",
    message: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<"idle" | "success" | "error">("idle");
  const [messageError, setMessageError] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    if (name === "message") {
      // Count characters (excluding leading/trailing whitespace for validation)
      const charCount = value.trim().length;
      if (charCount < 10) {
        setMessageError("Message must be at least 10 characters.");
      } else {
        setMessageError(null); // Clear error if within limit
      }
    }

    setFormData({ ...formData, [name]: value });
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check character count before submitting
    const charCount = formData.message.trim().length;
    if (charCount < 10) {
      setMessageError("Message must be at least 10 characters.");
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus("idle");

    try {
      const contactData: ContactFormData = {
        name: formData.name,
        email: formData.email,
        countryCode: formData.countryCode,
        phoneNumber: formData.phoneNumber,
        service: formData.service,
        message: formData.message,
      };

      console.log("Form Data being sent:", contactData);
      console.log("Phone Number:", formData.phoneNumber);
      console.log("Country Code:", formData.countryCode);

      const result = await submitContactForm(contactData);

      if (result.success) {
        setSubmitStatus("success");
        setFormData({
          name: "",
          email: "",
          countryCode: "+91",
          phoneNumber: "",
          service: "",
          message: "",
        });
        console.log("Form submitted successfully:", result.message);
      } else {
        setSubmitStatus("error");
        console.error("Form submission failed:", result.message || result.errors);
      }
    } catch (error) {
      setSubmitStatus("error");
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section className="bg-white dark:bg-black text-black dark:text-white py-16 px-4">
      <h2 className="text-center xl:text-[65px] lg:text-[55px] text-[50px] md:text-[72px] font-semibold mb-10 text-black dark:text-white">
        Questions? Feel Free to Reach<br></br> Out Via Message.
      </h2>

      <form
        onSubmit={handleSubmit}
        className="lg:max-w-[80%] xl:max-w-[80%] md:max-w-[80%] sm:max-w-[90%] max-w-[90%] mx-auto text-black dark:text-white bg-[#e9e9e9] dark:bg-zinc-900 rounded-xl xl:p-10 lg:p-2 md:p-4 p-6 space-y-6 w-[1800px]"
      >
        <div className="flex flex-col md:flex-col gap-20">
          <div className="flex xl:flex-row lg:flex-row md:flex-row flex-col w-full gap-10">
            <div className="xl:w-1/2 lg:w-1/2 md:w-1/2 w-full">
              <label className="block mb-1 text-[20px] text-black dark:text-white">Name</label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className="w-full bg-transparent border-b border-gray-400 dark:border-zinc-700 outline-none py-2 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 autofill:bg-transparent autofill:shadow-[inset_0_0_0px_1000px_transparent]"
                style={{
                  WebkitBoxShadow: "inset 0 0 0 1000px transparent",
                  WebkitTextFillColor: "inherit",
                  transition: "background-color 5000s ease-in-out 0s",
                }}
                placeholder="Your Name"
                required
              />
            </div>

            <div className="xl:w-1/2 lg:w-1/2 md:w-1/2 w-full">
              <label className="block mb-1 text-[20px] text-black dark:text-white">Email*</label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className="w-full bg-transparent border-b border-gray-400 dark:border-zinc-700 outline-none py-2 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                placeholder="<EMAIL>"
                required
              />
            </div>
          </div>

          <div className="flex xl:flex-row lg:flex-row md:flex-row flex-col w-full gap-10">
            <div className="xl:w-1/2 lg:w-1/2 md:w-1/2 w-full">
              <label className="block mb-1 text-[20px] text-black dark:text-white">Phone Number</label>
              <div className="flex gap-2">
                <div className="w-32">
                  <Select
                    value={formData.countryCode}
                    onValueChange={(value) => handleSelectChange("countryCode", value)}
                  >
                    <SelectTrigger className="bg-transparent border-b border-gray-400 dark:border-zinc-700 border-t-0 border-l-0 border-r-0 rounded-none text-black dark:text-white focus:ring-0 focus:ring-offset-0 focus:border-gray-400 dark:focus:border-zinc-700">
                      <SelectValue placeholder="Code" />
                    </SelectTrigger>
                    <SelectContent className="bg-white dark:bg-zinc-800 border-gray-300 dark:border-zinc-700 text-black dark:text-white">
                      <SelectItem value="+1">🇺🇸 +1</SelectItem>
                      <SelectItem value="+91">🇮🇳 +91</SelectItem>
                      <SelectItem value="+44">🇬🇧 +44</SelectItem>
                      <SelectItem value="+86">🇨🇳 +86</SelectItem>
                      <SelectItem value="+81">🇯🇵 +81</SelectItem>
                      <SelectItem value="+49">🇩🇪 +49</SelectItem>
                      <SelectItem value="+33">🇫🇷 +33</SelectItem>
                      <SelectItem value="+39">🇮🇹 +39</SelectItem>
                      <SelectItem value="+34">🇪🇸 +34</SelectItem>
                      <SelectItem value="+7">🇷🇺 +7</SelectItem>
                      <SelectItem value="+55">🇧🇷 +55</SelectItem>
                      <SelectItem value="+61">🇦🇺 +61</SelectItem>
                      <SelectItem value="+82">🇰🇷 +82</SelectItem>
                      <SelectItem value="+52">🇲🇽 +52</SelectItem>
                      <SelectItem value="+31">🇳🇱 +31</SelectItem>
                      <SelectItem value="+46">🇸🇪 +46</SelectItem>
                      <SelectItem value="+47">🇳🇴 +47</SelectItem>
                      <SelectItem value="+45">🇩🇰 +45</SelectItem>
                      <SelectItem value="+41">🇨🇭 +41</SelectItem>
                      <SelectItem value="+43">🇦🇹 +43</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex-1">
                  <input
                    type="tel"
                    name="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={handleChange}
                    className="w-full bg-transparent border-b border-gray-400 dark:border-zinc-700 outline-none py-2 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 autofill:bg-transparent autofill:shadow-[inset_0_0_0px_1000px_transparent]"
                    style={{
                      WebkitBoxShadow: "inset 0 0 0 1000px transparent",
                      WebkitTextFillColor: "inherit",
                      transition: "background-color 5000s ease-in-out 0s",
                    }}
                    placeholder="Your phone number"
                  />
                </div>
              </div>
            </div>

            <div className="flex flex-col xl:w-1/2 lg:w-1/2 md:w-1/2 w-full">
              <label className="block mb-1 xl:text-[20px] lg:text-[20px] md:text-[20px] text-[20px] text-black dark:text-white">
                Service
              </label>
              <input
                type="text"
                name="service"
                value={formData.service}
                onChange={handleChange}
                className="w-full bg-transparent border-b border-gray-400 dark:border-zinc-700 outline-none py-2 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                placeholder="What service are you interested in?"
              />
            </div>
          </div>

          <div>
            <label className="block mb-1 text-sm text-black dark:text-white">Your Message*</label>
            <textarea
              name="message"
              value={formData.message}
              onChange={handleChange}
              rows={4}
              className="w-full bg-transparent border-b border-gray-400 dark:border-zinc-700 outline-none py-2 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none"
              placeholder="Write your message here..."
              required
            />
            {messageError && (
              <div className="text-red-400 text-sm mt-1">{messageError}</div>
            )}
            {/* <div className="text-gray-500 dark:text-gray-400 text-sm mt-1">
              Character count: {formData.message.trim().length}/10
            </div> */}
          </div>

          {submitStatus === "success" && (
            <div className="text-green-400 text-center py-2">
              Message sent successfully! We'll get back to you soon.
            </div>
          )}
          {submitStatus === "error" && (
            <div className="text-red-400 text-center py-2">
              Failed to send message. Please try again.
            </div>
          )}

          <button
            type="submit"
            disabled={isSubmitting || !!messageError}
            className="w-full bg-orange-500 hover:bg-orange-600 disabled:bg-orange-400 disabled:cursor-not-allowed transition-colors text-white py-2 rounded-md font-medium"
          >
            {isSubmitting ? "SENDING..." : "SEND MESSAGE"}
          </button>
        </div>
      </form>
    </section>
  );
};

export default ContactForm;