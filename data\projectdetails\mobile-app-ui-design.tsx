// Mobile App UI Design Project Details
import img1 from "@/public/new assests/proejct/hotel.png"
import subimg1 from "@/public/new assests/proejct/subimage/Image 2.png"
import subimg2 from "@/public/new assests/proejct/subimage/image (1).png"
import subimg3 from "@/public/new assests/proejct/subimage/image.png"

export const mobileAppUIDesignDetails = {
  id: "01",
  uniqueId: "mobile-app-ui-design",
  title: "Mobile app UI Design & Branding",
  category: "Application Design",
  overview: {
    description: "We created a comprehensive mobile application design that focuses on user experience and modern aesthetics. The project involved creating intuitive interfaces that deliver exceptional user experiences while maintaining brand consistency throughout the application.",
    description2: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullaum laboris nisi ut aliquip ex ea commodo consequat."
  },
  projectDetails: {
    client: "Tech Startup",
    duration: "3 Months",
    categories: "ecommerce App",
    website: "www.ecommercephone.com",
  },
  processSteps: [
 
    {
      step: "02", 
      title: "Process & Challenge",
      description: "The design process involved multiple iterations and user testing sessions. Key challenges included Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullaum laboris nisi ut aliquip ex ea commodo consequat",
      description2: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et.consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et.consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et.",
      points: [
        "Creating intuitive navigation for complex features",
        "Ensuring accessibility across different user groups", 
        "Maintaining performance while adding rich interactions",
        "Balancing aesthetics with functionality"
      ]
    },
    {
      step: "03",
      title: "Summary",
      description: "The final product successfully delivered a modern, user-friendly mobile application that exceeded client expectations. The design system we created allows for easy scalability and future feature additions."
    }
  ],
  images: {
    hero: img1,
    gallery: [subimg1, subimg2, subimg3] 
  },
  technologies: ["React Native", "TypeScript", "Expo"],
  features: [
    "Responsive Design",
    "Dark/Light Mode",
    "Gesture Navigation",
    "Offline Support"
  ],
  nextProject: {
    id: "02",
    title: "SAAS User Interface",
    slug: "saas-user-interface"
  },
  previousProject: {
    id: "06", 
    title: "Ecommerce Site Design",
    slug: "ecommerce-site-design"
  }
};
