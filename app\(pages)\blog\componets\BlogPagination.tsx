"use client";
import React from "react";

interface BlogPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export default function BlogPagination({
  currentPage,
  totalPages,
  onPageChange,
}: BlogPaginationProps) {
  if (totalPages <= 1) return null;

  // Show limited page numbers on mobile
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = window.innerWidth < 768 ? 3 : 5;

    if (totalPages <= maxVisiblePages) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = startPage + maxVisiblePages - 1;

    if (endPage > totalPages) {
      endPage = totalPages;
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    if (startPage > 1) {
      pages.push(1);
      if (startPage > 2) {
        pages.push("...");
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push("...");
      }
      pages.push(totalPages);
    }

    return pages;
  };

  return (
    <div className="flex justify-center items-center gap-2 md:gap-4 mt-6 md:mt-8">
      {/* Previous Button */}
      {currentPage > 1 && (
        <button
          onClick={() => onPageChange(currentPage - 1)}
          className="px-3 py-1.5 md:px-4 md:py-2 rounded-lg transition-all duration-300 bg-zinc-800 text-white hover:bg-zinc-700 text-sm md:text-base"
        >
          Previous
        </button>
      )}

      {/* Page Numbers */}
      {getPageNumbers().map((pageNumber, index) => (
        <button
          key={index}
          onClick={() =>
            typeof pageNumber === "number" ? onPageChange(pageNumber) : null
          }
          className={`w-8 h-8 md:w-12 md:h-12 rounded-lg transition-all duration-300 text-sm md:text-base ${
            currentPage === pageNumber
              ? "bg-orange-500 text-white font-bold"
              : typeof pageNumber === "number"
              ? "bg-zinc-800 text-white hover:bg-zinc-700"
              : "bg-transparent text-white cursor-default"
          }`}
          disabled={pageNumber === "..."}
        >
          {pageNumber}
        </button>
      ))}

      {/* Next Button */}
      {currentPage < totalPages && (
        <button
          onClick={() => onPageChange(currentPage + 1)}
          className="px-3 py-1.5 md:px-4 md:py-2 rounded-lg transition-all duration-300 bg-zinc-800 text-white hover:bg-zinc-700 text-sm md:text-base"
        >
          Next
        </button>
      )}
    </div>
  );
}
