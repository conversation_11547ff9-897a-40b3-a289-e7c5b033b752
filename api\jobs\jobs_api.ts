// src/api/contactApi.ts
import apiClient from "@/lib/apiclient";
// FAQ interface based on API response







// Job Profile interface based on API response
export interface JobProfile {
    _id: string;
    id: number;
    jobTitle: string;
    jobDescription: string;
    techStack: string[];
    startDateApplied: string;
    lastDayApplied: string;
    status: 'active' | 'inactive';
    experienceRequired?: string;
    salaryRange?: string;
    location?: string;
    jobType: 'full-time' | 'part-time' | 'contract' | 'internship' | 'freelance';
    department?: string;
    requirements?: string[];
    benefits?: string[];
    applicationCount: number;
    isUrgent: boolean;
    postedBy?: string;
    createdAt: string;
    updatedAt: string;
    __v: number;
}

// API Response interface
export interface JobProfileApiResponse {
    status: boolean;
    code: number;
    message: string;
    data: JobProfile[];
    pagination?: {
        currentPage: number;
        totalPages: number;
        totalItems: number;
        itemsPerPage: number;
        hasNextPage: boolean;
        hasPrevPage: boolean;
    };
}

// Single Job Profile Response interface
export interface SingleJobProfileResponse {
    status: boolean;
    code: number;
    message: string;
    data: JobProfile;
}

// Job Profile Statistics interface
export interface JobProfileStatistics {
    total: number;
    active: number;
    inactive: number;
    byJobType: Array<{ jobType: string; count: number }>;
    byDepartment: Array<{ department: string; count: number }>;
    totalApplications: number;
}

// Create Job Profile interface
export interface CreateJobProfileData {
    jobTitle: string;
    jobDescription: string;
    techStack: string[];
    startDateApplied: string;
    lastDayApplied: string;
    status?: 'active' | 'inactive';
    experienceRequired?: string;
    salaryRange?: string;
    location?: string;
    jobType?: 'full-time' | 'part-time' | 'contract' | 'internship' | 'freelance';
    department?: string;
    requirements?: string[];
    benefits?: string[];
    isUrgent?: boolean;
    postedBy?: string;
}

// Update Job Profile interface
export interface UpdateJobProfileData {
    jobTitle?: string;
    jobDescription?: string;
    techStack?: string[];
    startDateApplied?: string;
    lastDayApplied?: string;
    status?: 'active' | 'inactive';
    experienceRequired?: string;
    salaryRange?: string;
    location?: string;
    jobType?: 'full-time' | 'part-time' | 'contract' | 'internship' | 'freelance';
    department?: string;
    requirements?: string[];
    benefits?: string[];
    isUrgent?: boolean;
    postedBy?: string;
}

// Get all job profiles with optional filters
export const getAllJobProfiles = async (params?: {
    page?: number;
    limit?: number;
    status?: string;
    techStack?: string;
    jobType?: string;
    department?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    isAcceptingApplications?: boolean;
}): Promise<JobProfileApiResponse> => {
    try {
        const queryParams = new URLSearchParams();
        
        if (params?.page) queryParams.append('page', params.page.toString());
        if (params?.limit) queryParams.append('limit', params.limit.toString());
        if (params?.status) queryParams.append('status', params.status);
        if (params?.techStack) queryParams.append('techStack', params.techStack);
        if (params?.jobType) queryParams.append('jobType', params.jobType);
        if (params?.department) queryParams.append('department', params.department);
        if (params?.search) queryParams.append('search', params.search);
        if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
        if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);
        if (params?.isAcceptingApplications !== undefined) {
            queryParams.append('isAcceptingApplications', params.isAcceptingApplications.toString());
        }

        const url = `/jobs${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const response = await apiClient.get(url);
        
        return response.data;
    } catch (error) {
        console.error('Error fetching job profiles:', error);
        throw error;
    }
};

// Get active job profiles (public endpoint)
export const getActiveJobProfiles = async (): Promise<JobProfileApiResponse> => {
    try {
        const response = await apiClient.get('/jobs/active');
        return response.data;
    } catch (error) {
        console.error('Error fetching active job profiles:', error);
        throw error;
    }
};

// Get job profile by ID
export const getJobProfileById = async (id: number): Promise<SingleJobProfileResponse> => {
    try {
        const response = await apiClient.get(`/jobs/${id}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching job profile by ID:', error);
        throw error;
    }
};

// Create new job profile
export const createJobProfile = async (jobData: CreateJobProfileData): Promise<SingleJobProfileResponse> => {
    try {
        const response = await apiClient.post('/jobs', jobData);
        return response.data;
    } catch (error) {
        console.error('Error creating job profile:', error);
        throw error;
    }
};

// Update job profile by ID
export const updateJobProfile = async (id: number, jobData: UpdateJobProfileData): Promise<SingleJobProfileResponse> => {
    try {
        const response = await apiClient.put(`/jobs/${id}`, jobData);
        return response.data;
    } catch (error) {
        console.error('Error updating job profile:', error);
        throw error;
    }
};

// Delete job profile by ID
export const deleteJobProfile = async (id: number): Promise<{ status: boolean; message: string }> => {
    try {
        const response = await apiClient.delete(`/jobs/${id}`);
        return response.data;
    } catch (error) {
        console.error('Error deleting job profile:', error);
        throw error;
    }
};

// Get jobs by tech stack
export const getJobsByTechStack = async (techStack: string): Promise<JobProfileApiResponse> => {
    try {
        const response = await apiClient.get(`/jobs/techstack/${techStack}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching jobs by tech stack:', error);
        throw error;
    }
};

// Get job profile statistics
export const getJobProfileStatistics = async (): Promise<{ status: boolean; data: JobProfileStatistics }> => {
    try {
        const response = await apiClient.get('/jobs/statistics');
        return response.data;
    } catch (error) {
        console.error('Error fetching job profile statistics:', error);
        throw error;
    }
};

// Utility function to check if job is accepting applications
export const isJobAcceptingApplications = (job: JobProfile): boolean => {
    const now = new Date();
    const startDate = new Date(job.startDateApplied);
    const endDate = new Date(job.lastDayApplied);
    
    return job.status === 'active' && now >= startDate && now <= endDate;
};

// Utility function to format job date
export const formatJobDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
};

// Export default
export default {
    getAllJobProfiles,
    getActiveJobProfiles,
    getJobProfileById,
    createJobProfile,
    updateJobProfile,
    deleteJobProfile,
    getJobsByTechStack,
    getJobProfileStatistics,
    isJobAcceptingApplications,
    formatJobDate
};
