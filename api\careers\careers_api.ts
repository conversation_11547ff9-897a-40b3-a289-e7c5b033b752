// src/api/careerApi.ts
import apiClient from "@/lib/apiclient";

// Career Application interface based on API response
export interface CareerApplication {
    id: number;
    name: string;
    email: string;
    jobDescription: string;
    cvUrl: string;
    techStack: string;
    whyHireYou: string;
    status: 'new' | 'reviewing' | 'shortlisted' | 'interviewed' | 'hired' | 'rejected';
    experience?: string;
    expectedSalary?: string;
    availableFrom?: string;
    phoneNumber?: string;
    linkedinProfile?: string;
    githubProfile?: string;
    portfolioUrl?: string;
    notes?: string;
    reviewedBy?: string;
    reviewedAt?: string;
    createdAt: string;
    updatedAt: string;
}

// API Response interface
export interface CareerApplicationApiResponse {
    status: boolean;
    code: number;
    message: string;
    data: CareerApplication[];
    pagination?: {
        currentPage: number;
        totalPages: number;
        totalItems: number;
        itemsPerPage: number;
        hasNextPage: boolean;
        hasPrevPage: boolean;
    };
}

// Single Career Application Response interface
export interface SingleCareerApplicationResponse {
    status: boolean;
    code: number;
    message: string;
    data: CareerApplication;
}

// Career Application Statistics interface
export interface CareerApplicationStatistics {
    total: number;
    new: number;
    reviewing: number;
    shortlisted: number;
    interviewed: number;
    hired: number;
    rejected: number;
    byJobDescription: Array<{ jobDescription: string; count: number }>;
}

// Create Career Application interface
export interface CreateCareerApplicationData {
    name: string;
    email: string;
    jobDescription: string;
    techStack: string;
    whyHireYou: string;
    experience?: string;
    expectedSalary?: string;
    availableFrom?: string;
    phoneNumber?: string;
    linkedinProfile?: string;
    githubProfile?: string;
    portfolioUrl?: string;
    cv: File; // CV file for upload
}

// Update Career Application interface
export interface UpdateCareerApplicationData {
    name?: string;
    email?: string;
    jobDescription?: string;
    techStack?: string;
    whyHireYou?: string;
    status?: 'new' | 'reviewing' | 'shortlisted' | 'interviewed' | 'hired' | 'rejected';
    experience?: string;
    expectedSalary?: string;
    availableFrom?: string;
    phoneNumber?: string;
    linkedinProfile?: string;
    githubProfile?: string;
    portfolioUrl?: string;
    notes?: string;
    reviewedBy?: string;
    cv?: File; // Optional CV file for update
}

// Submit career application with CV upload
export const submitCareerApplication = async (
    applicationData: CreateCareerApplicationData
): Promise<SingleCareerApplicationResponse> => {
    try {
        const formData = new FormData();
        
        // Append all form fields
        formData.append('name', applicationData.name);
        formData.append('email', applicationData.email);
        formData.append('jobDescription', applicationData.jobDescription);
        formData.append('techStack', applicationData.techStack);
        formData.append('whyHireYou', applicationData.whyHireYou);
        
        // Append optional fields
        if (applicationData.experience) formData.append('experience', applicationData.experience);
        if (applicationData.expectedSalary) formData.append('expectedSalary', applicationData.expectedSalary);
        if (applicationData.availableFrom) formData.append('availableFrom', applicationData.availableFrom);
        if (applicationData.phoneNumber) formData.append('phoneNumber', applicationData.phoneNumber);
        if (applicationData.linkedinProfile) formData.append('linkedinProfile', applicationData.linkedinProfile);
        if (applicationData.githubProfile) formData.append('githubProfile', applicationData.githubProfile);
        if (applicationData.portfolioUrl) formData.append('portfolioUrl', applicationData.portfolioUrl);
        
        // Append CV file
        formData.append('cv', applicationData.cv);

        const response = await apiClient.post('/careers', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            }
        });
        
        return response.data;
    } catch (error: any) {
        console.error('Error submitting career application:', error);
        
        // Enhanced error handling
        if (error.response) {
            // Backend returned an error response
            throw new Error(error.response.data?.message || 'Failed to submit career application');
        } else if (error.request) {
            // Network error
            throw new Error('Network error. Please check your connection and try again.');
        } else {
            // Other error
            throw new Error('An unexpected error occurred. Please try again later.');
        }
    }
};

// Get all career applications with optional filters
export const getAllCareerApplications = async (params?: {
    page?: number;
    limit?: number;
    status?: string;
    jobDescription?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}): Promise<CareerApplicationApiResponse> => {
    try {
        const queryParams = new URLSearchParams();
        
        if (params?.page) queryParams.append('page', params.page.toString());
        if (params?.limit) queryParams.append('limit', params.limit.toString());
        if (params?.status) queryParams.append('status', params.status);
        if (params?.jobDescription) queryParams.append('jobDescription', params.jobDescription);
        if (params?.search) queryParams.append('search', params.search);
        if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
        if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);

        const url = `/careers${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const response = await apiClient.get(url);
        
        return response.data;
    } catch (error: any) {
        console.error('Error fetching career applications:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch career applications');
    }
};

// Get career application by ID
export const getCareerApplicationById = async (id: number): Promise<SingleCareerApplicationResponse> => {
    try {
        const response = await apiClient.get(`/careers/${id}`);
        return response.data;
    } catch (error: any) {
        console.error('Error fetching career application by ID:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch career application');
    }
};

// Update career application by ID
export const updateCareerApplication = async (
    id: number, 
    applicationData: UpdateCareerApplicationData
): Promise<SingleCareerApplicationResponse> => {
    try {
        let response;
        
        if (applicationData.cv) {
            // If CV file is included, use FormData
            const formData = new FormData();
            
            Object.entries(applicationData).forEach(([key, value]) => {
                if (value !== undefined && value !== null) {
                    if (key === 'cv' && value instanceof File) {
                        formData.append(key, value);
                    } else {
                        formData.append(key, value.toString());
                    }
                }
            });

            response = await apiClient.put(`/careers/${id}`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                }
            });
        } else {
            // If no CV file, use regular JSON
            response = await apiClient.put(`/careers/${id}`, applicationData);
        }
        
        return response.data;
    } catch (error: any) {
        console.error('Error updating career application:', error);
        throw new Error(error.response?.data?.message || 'Failed to update career application');
    }
};

// Delete career application by ID
export const deleteCareerApplication = async (id: number): Promise<{ status: boolean; message: string }> => {
    try {
        const response = await apiClient.delete(`/careers/${id}`);
        return response.data;
    } catch (error: any) {
        console.error('Error deleting career application:', error);
        throw new Error(error.response?.data?.message || 'Failed to delete career application');
    }
};

// Get applications by job description
export const getApplicationsByJobDescription = async (
    jobDescription: string
): Promise<CareerApplicationApiResponse> => {
    try {
        const response = await apiClient.get(`/careers/job/${encodeURIComponent(jobDescription)}`);
        return response.data;
    } catch (error: any) {
        console.error('Error fetching applications by job description:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch applications by job description');
    }
};

// Get career application statistics
export const getCareerApplicationStatistics = async (): Promise<{
    status: boolean;
    data: CareerApplicationStatistics;
}> => {
    try {
        const response = await apiClient.get('/careers/statistics');
        return response.data;
    } catch (error: any) {
        console.error('Error fetching career application statistics:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch career statistics');
    }
};

// Utility function to get application status color
export const getApplicationStatusColor = (status: string): string => {
    switch (status) {
        case 'new': return 'text-blue-500';
        case 'reviewing': return 'text-yellow-500';
        case 'shortlisted': return 'text-purple-500';
        case 'interviewed': return 'text-orange-500';
        case 'hired': return 'text-green-500';
        case 'rejected': return 'text-red-500';
        default: return 'text-gray-500';
    }
};

// Utility function to format application date
export const formatApplicationDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

// Utility function to validate CV file
export const validateCVFile = (file: File): { isValid: boolean; error?: string } => {
    const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    const maxSize = 10 * 1024 * 1024; // 10MB

    if (!allowedTypes.includes(file.type)) {
        return { isValid: false, error: 'Only PDF, DOC, and DOCX files are allowed' };
    }

    if (file.size > maxSize) {
        return { isValid: false, error: 'File size must be less than 10MB' };
    }

    return { isValid: true };
};

export default {
    submitCareerApplication,
    getAllCareerApplications,
    getCareerApplicationById,
    updateCareerApplication,
    deleteCareerApplication,
    getApplicationsByJobDescription,
    getCareerApplicationStatistics,
    getApplicationStatusColor,
    formatApplicationDate,
    validateCVFile
};