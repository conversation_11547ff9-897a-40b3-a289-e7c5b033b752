"use client";

import React, { useState } from 'react';
import {
  <PERSON>X,
  <PERSON><PERSON>ser,
  FiMail,
  FiPhone,
  FiUpload,
  FiCheck,
  FiArrowLeft,
  FiArrowRight,
  FiAlertCircle,
  FiCheckCircle,
  FiLinkedin,
  FiGithub,
  FiGlobe
} from 'react-icons/fi';
import { submitCareerApplication, validateCVFile, CreateCareerApplicationData } from '@/api/careers/careers_api';
import { JobProfile } from '@/api/jobs/jobs_api';
import { toast } from 'react-hot-toast';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface CareerApplicationModalProps {
  job: JobProfile;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface FormData {
  // Step 1: Basic Information
  name: string;
  email: string;
  phoneNumber: string;
  experience: string;
  expectedSalary: string;
  availableFrom: string;
  
  // Step 2: Professional Information
  techStack: string;
  whyHireYou: string;
  linkedinProfile: string;
  githubProfile: string;
  portfolioUrl: string;
  cv: File | null;
}

interface FormErrors {
  [key: string]: string;
}

const CareerApplicationModal: React.FC<CareerApplicationModalProps> = ({
  job,
  isOpen,
  onClose,
  onSuccess
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [submitting, setSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phoneNumber: '',
    experience: '',
    expectedSalary: '',
    availableFrom: '',
    techStack: '',
    whyHireYou: '',
    linkedinProfile: '',
    githubProfile: '',
    portfolioUrl: '',
    cv: null
  });

  const [formErrors, setFormErrors] = useState<FormErrors>({});

  const validateStep1 = (): boolean => {
    const errors: FormErrors = {};

    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    } else if (formData.name.trim().length < 2) {
      errors.name = 'Name must be at least 2 characters';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (formData.phoneNumber && !/^\+?[\d\s\-\(\)]{10,}$/.test(formData.phoneNumber)) {
      errors.phoneNumber = 'Please enter a valid phone number';
    }

    if (formData.linkedinProfile && !formData.linkedinProfile.includes('linkedin.com')) {
      errors.linkedinProfile = 'Please enter a valid LinkedIn URL';
    }

    if (formData.githubProfile && !formData.githubProfile.includes('github.com')) {
      errors.githubProfile = 'Please enter a valid GitHub URL';
    }

    if (formData.portfolioUrl && !/^https?:\/\/.+/.test(formData.portfolioUrl)) {
      errors.portfolioUrl = 'Please enter a valid URL (starting with http:// or https://)';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validateStep2 = (): boolean => {
    const errors: FormErrors = {};

    if (!formData.techStack.trim()) {
      errors.techStack = 'Tech stack is required';
    } else if (formData.techStack.trim().length < 10) {
      errors.techStack = 'Please provide more details about your tech stack';
    }

    if (!formData.whyHireYou.trim()) {
      errors.whyHireYou = 'This field is required';
    } else if (formData.whyHireYou.trim().length < 50) {
      errors.whyHireYou = 'Please provide at least 50 characters explaining why we should hire you';
    } else if (formData.whyHireYou.trim().length > 1000) {
      errors.whyHireYou = 'Please keep your response under 1000 characters';
    }

    if (!formData.cv) {
      errors.cv = 'CV/Resume is required';
    } else {
      const cvValidation = validateCVFile(formData.cv);
      if (!cvValidation.isValid) {
        errors.cv = cvValidation.error || 'Invalid CV file';
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error for this field
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, cv: file }));
      if (formErrors.cv) {
        setFormErrors(prev => ({ ...prev, cv: '' }));
      }
    }
  };

  const handleNextStep = () => {
    if (currentStep === 1 && validateStep1()) {
      setCurrentStep(2);
    }
  };

  const handlePrevStep = () => {
    if (currentStep === 2) {
      setCurrentStep(1);
    }
  };

  const handleSubmit = async () => {
    if (!validateStep2()) return;

    try {
      setSubmitting(true);
      setError(null);

      const applicationData: CreateCareerApplicationData = {
        name: formData.name.trim(),
        email: formData.email.trim(),
        jobDescription: job.jobTitle,
        techStack: formData.techStack.trim(),
        whyHireYou: formData.whyHireYou.trim(),
        cv: formData.cv!,
        ...(formData.phoneNumber && { phoneNumber: formData.phoneNumber.trim() }),
        ...(formData.experience && { experience: formData.experience.trim() }),
        ...(formData.expectedSalary && { expectedSalary: formData.expectedSalary.trim() }),
        ...(formData.availableFrom && { availableFrom: formData.availableFrom }),
        ...(formData.linkedinProfile && { linkedinProfile: formData.linkedinProfile.trim() }),
        ...(formData.githubProfile && { githubProfile: formData.githubProfile.trim() }),
        ...(formData.portfolioUrl && { portfolioUrl: formData.portfolioUrl.trim() })
      };

      await submitCareerApplication(applicationData);
      setSubmitSuccess(true);

      // Show success toast
      toast.success('Application submitted successfully! We will review your application and get back to you soon.', {
        duration: 5000,
        position: 'top-right',
      });

      // Auto close after 3 seconds
      setTimeout(() => {
        onSuccess();
      }, 3000);

    } catch (err: any) {
      console.error('Error submitting application:', err);
      const errorMessage = err.response?.data?.message || 'Failed to submit application. Please try again.';
      setError(errorMessage);

      // Show error toast
      toast.error(errorMessage, {
        duration: 5000,
        position: 'top-right',
      });
    } finally {
      setSubmitting(false);
    }
  };

  if (submitSuccess) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="bg-[#1C1C1C] border-none max-w-md">
          <div className="text-center p-4">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <FiCheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-xl font-semibold text-white/60 mb-2">
              Application Submitted Successfully!
            </h3>
            <p className="text-white/80 mb-6">
              Thank you for your interest in the {job.jobTitle} position. We'll review your application and get back to you soon.
            </p>
            <button
              onClick={onSuccess}
              className="w-full px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
            >
              Close
            </button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-[#343434] border-none max-w-2xl max-h-[90vh] overflow-y-auto p-0">
        {/* Header */}
        <DialogHeader className="p-6 border-b border-gray-200">
          <DialogTitle className="text-xl font-semibold text-white/60 text-left">
            Apply for Position
          </DialogTitle>
          <p className="text-sm text-white/40 text-left">{job.jobTitle}</p>
        </DialogHeader>

        {/* Progress Bar */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center">
            <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
              currentStep >= 1 ? 'bg-orange-600 text-white' : 'bg-white/20 text-white/80'
            }`}>
              {currentStep > 1 ? <FiCheck className="w-4 h-4" /> : '1'}
            </div>
            <div className={`flex-1 h-1 mx-4 ${
              currentStep >= 2 ? 'bg-orange-600' : 'bg-gray-200'
            }`} />
            <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
              currentStep >= 2 ? 'bg-orange-600 text-white' : 'bg-white/20 text-white/80'
            }`}>
              2
            </div>
          </div>
          <div className="flex justify-between mt-2">
            <span className="text-xs text-white/80">Basic Information</span>
            <span className="text-xs text-white/80">Professional Details</span>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mx-6 mt-4 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <FiAlertCircle className="h-5 w-5 text-red-400 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Form Content */}
        <div className="p-6">
          {currentStep === 1 ? (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-white/60 mb-4">Basic Information</h3>
              
              {/* Name */}
              <div>
                <label className="block text-sm font-medium text-white/80 mb-2">
                  <FiUser className="inline mr-2 h-4 w-4" />
                  Full Name *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-transparent border-b text-white/80 outline-none ${
                    formErrors.name ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Your full name"
                />
                {formErrors.name && <p className="mt-1 text-sm text-red-600">{formErrors.name}</p>}
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-white/80 mb-2">
                  <FiMail className="inline mr-2 h-4 w-4" />
                  Email Address *
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-transparent border-b text-white/80 outline-none ${
                    formErrors.email ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="<EMAIL>"
                />
                {formErrors.email && <p className="mt-1 text-sm text-red-600">{formErrors.email}</p>}
              </div>

              {/* Phone */}
              <div>
                <label className="block text-sm font-medium text-white/80 mb-2">
                  <FiPhone className="inline mr-2 h-4 w-4" />
                  Phone Number
                </label>
                <input
                  type="tel"
                  name="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-transparent border-b text-white/80 outline-none ${
                    formErrors.phoneNumber ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="+****************"
                />
                {formErrors.phoneNumber && <p className="mt-1 text-sm text-red-600">{formErrors.phoneNumber}</p>}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Experience */}
                <div>
                  <label className="block text-sm font-medium text-white/80 mb-2">
                    Years of Experience
                  </label>
                  <input
                    type="text"
                    name="experience"
                    value={formData.experience}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-transparent border-b border-gray-300 text-white/80 outline-none"
                    placeholder="e.g., 3-5 years"
                  />
                </div>

                {/* Expected Salary */}
                <div>
                  <label className="block text-sm font-medium text-white/80 mb-2">
                    Expected Salary
                  </label>
                  <input
                    type="text"
                    name="expectedSalary"
                    value={formData.expectedSalary}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-transparent border-b border-gray-300 text-white/80 outline-none"
                    placeholder="e.g., $80,000 - $100,000"
                  />
                </div>
              </div>

              {/* Available From */}
              <div>
                <label className="block text-sm font-medium text-white/80 mb-2">
                  Available From
                </label>
                <input
                  type="date"
                  name="availableFrom"
                  value={formData.availableFrom}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-transparent border-b border-gray-300 text-white/80 outline-none"
                />
              </div>

              {/* LinkedIn */}
              <div>
                <label className="block text-sm font-medium text-white/80 mb-2">
                  <FiLinkedin className="inline mr-2 h-4 w-4" />
                  LinkedIn Profile
                </label>
                <input
                  type="url"
                  name="linkedinProfile"
                  value={formData.linkedinProfile}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-transparent border-b text-white/80 outline-none ${
                    formErrors.linkedinProfile ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="https://linkedin.com/in/yourprofile"
                />
                {formErrors.linkedinProfile && <p className="mt-1 text-sm text-red-600">{formErrors.linkedinProfile}</p>}
              </div>

              {/* GitHub */}
              <div>
                <label className="block text-sm font-medium text-white/80 mb-2">
                  <FiGithub className="inline mr-2 h-4 w-4" />
                  GitHub Profile
                </label>
                <input
                  type="url"
                  name="githubProfile"
                  value={formData.githubProfile}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-transparent border-b text-white/80 outline-none ${
                    formErrors.githubProfile ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="https://github.com/yourusername"
                />
                {formErrors.githubProfile && <p className="mt-1 text-sm text-red-600">{formErrors.githubProfile}</p>}
              </div>

              {/* Portfolio */}
              <div>
                <label className="block text-sm font-medium text-white/80 mb-2">
                  <FiGlobe className="inline mr-2 h-4 w-4" />
                  Portfolio Website
                </label>
                <input
                  type="url"
                  name="portfolioUrl"
                  value={formData.portfolioUrl}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-transparent border-b text-white/80 outline-none ${
                    formErrors.portfolioUrl ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="https://yourportfolio.com"
                />
                {formErrors.portfolioUrl && <p className="mt-1 text-sm text-red-600">{formErrors.portfolioUrl}</p>}
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-white/60 mb-4">Professional Details</h3>
              
              {/* Tech Stack */}
              <div>
                <label className="block text-sm font-medium text-white/80 mb-2">
                  Technical Skills & Experience *
                </label>
                <textarea
                  name="techStack"
                  value={formData.techStack}
                  onChange={handleInputChange}
                  rows={4}
                  className={`w-full px-4 py-3 bg-transparent border-b text-white/80 outline-none resize-vertical ${
                    formErrors.techStack ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Describe your technical skills, programming languages, frameworks, tools, and relevant experience..."
                />
                {formErrors.techStack && <p className="mt-1 text-sm text-red-600">{formErrors.techStack}</p>}
              </div>

              {/* Why Hire You */}
              <div>
                <label className="block text-sm font-medium text-white/80 mb-2">
                  Why should we hire you? *
                </label>
                <textarea
                  name="whyHireYou"
                  value={formData.whyHireYou}
                  onChange={handleInputChange}
                  rows={5}
                  className={`w-full px-4 py-3 bg-transparent border-b text-white/80 outline-none resize-vertical ${
                    formErrors.whyHireYou ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Tell us what makes you the perfect candidate for this position. Highlight your unique skills, achievements, and what you can bring to our team..."
                />
                <div className="flex justify-between items-center mt-1">
                  {formErrors.whyHireYou ? (
                    <p className="text-sm text-red-600">{formErrors.whyHireYou}</p>
                  ) : (
                    <p className="text-sm text-gray-500">
                      {formData.whyHireYou.length}/1000 characters
                    </p>
                  )}
                </div>
              </div>

              {/* CV Upload */}
              <div>
                <label className="block text-sm font-medium text-white/80 mb-2">
                  <FiUpload className="inline mr-2 h-4 w-4" />
                  Upload CV/Resume *
                </label>
                <div className={`border-2 border-dashed rounded-lg p-6 text-center ${
                  formErrors.cv ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400'
                }`}>
                  <input
                    type="file"
                    accept=".pdf,.doc,.docx"
                    onChange={handleFileChange}
                    className="hidden"
                    id="cv-upload"
                  />
                  <label htmlFor="cv-upload" className="cursor-pointer">
                    <FiUpload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <p className="text-sm text-white/80">
                      {formData.cv ? formData.cv.name : 'Click to upload your CV/Resume'}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      PDF, DOC, or DOCX (max 10MB)
                    </p>
                  </label>
                </div>
                {formErrors.cv && <p className="mt-1 text-sm text-red-600">{formErrors.cv}</p>}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <div>
            {currentStep === 2 && (
              <button
                onClick={handlePrevStep}
                className="flex items-center px-4 py-2 text-white/80 hover:text-gray-800 transition-colors"
              >
                <FiArrowLeft className="mr-2 h-4 w-4" />
                Previous
              </button>
            )}
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 text-white/80 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            
            {currentStep === 1 ? (
              <button
                onClick={handleNextStep}
                className="flex items-center px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
              >
                Next
                <FiArrowRight className="ml-2 h-4 w-4" />
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                disabled={submitting}
                className="flex items-center px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {submitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Submitting...
                  </>
                ) : (
                  <>
                    <FiCheck className="mr-2 h-4 w-4" />
                    Submit Application
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CareerApplicationModal;
