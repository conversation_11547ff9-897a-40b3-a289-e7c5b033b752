// src/api/contactApi.ts
import apiClient from "@/lib/apiclient";
// FAQ interface based on API response






// Team member interface based on API response
export interface TeamMember {
    _id: string;
    name: string;
    jobCategory: string;
    imageUrl: string;
    status: string;
    socialMedia: {
        website: string | null;
        facebook: string | null;
        linkedin: string | null;
        instagram: string | null;
        github: string | null;
    };
    createdAt: string;
    updatedAt: string;
    id: number;
    __v: number;
}

// Pagination interface
export interface Pagination {
    currentPage: number;
    totalPages: number;
    totalCount: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
}

// API Response interface for get all team members
export interface TeamResponse {
    status: boolean;
    code: number;
    message: string;
    data: {
        teamMembers: TeamMember[];
        pagination: Pagination;
    };
}

// Get all team members with pagination support
export const getAllTeamMembers = async (page: number = 1, limit: number = 100): Promise<{teamMembers: TeamMember[], pagination: Pagination}> => {
    try {
        const response = await apiClient.get<TeamResponse>(`/team?page=${page}&limit=${limit}`);
        return {
            teamMembers: response.data.data.teamMembers,
            pagination: response.data.data.pagination
        };
    } catch (error: any) {
        console.error('Error fetching team members:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch team members');
    }
};

// Get ALL team members across all pages - FIXED VERSION
export const getAllTeamMembersComplete = async (): Promise<TeamMember[]> => {
    try {
        let allTeamMembers: TeamMember[] = [];
        let currentPage = 1;
        let hasMorePages = true;
        
        while (hasMorePages) {
            const response = await getAllTeamMembers(currentPage, 50);
            allTeamMembers = [...allTeamMembers, ...response.teamMembers];
            
            hasMorePages = response.pagination.hasNextPage;
            currentPage++;
        }

        // Apply your sorting logic to all members
        const reversedTeamMembers = [...allTeamMembers].reverse();

        const sortedTeamMembers = reversedTeamMembers.sort((a, b) => {
            const aIsFounder = a.jobCategory.toLowerCase().includes('founder');
            const bIsFounder = b.jobCategory.toLowerCase().includes('founder');

            if (aIsFounder === bIsFounder) {
                return 0;
            }

            if (aIsFounder && !bIsFounder) {
                return -1;
            }

            return 1;
        });

        return sortedTeamMembers; // This returns ONLY the array
    } catch (error: any) {
        console.error('Error fetching all team members:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch team members');
    }
};
// Transform API team data for component use
export interface TeamCardData {
    id: string;
    name: string;
    role: string;
    image: string;
    socialMedia: {
        facebook: string | null;
        twitter: string | null; // Using website as twitter for compatibility
        linkedin: string | null;
        instagram: string | null;
        github: string | null;
    };
}

export const transformTeamMemberToCard = (member: TeamMember): TeamCardData => {
    return {
        id: member._id,
        name: member.name,
        role: member.jobCategory,
        image: member.imageUrl,
        socialMedia: {
            facebook: member.socialMedia.instagram, // Using instagram as facebook for icon compatibility
            twitter: member.socialMedia.website,    // Using website as twitter for icon compatibility
            linkedin: member.socialMedia.linkedin,
            instagram: member.socialMedia.instagram,
            github: member.socialMedia.github
        }
    };
};
