'use client';

import { useState } from 'react';

interface FAQItem {
  question: string;
  answer: string;
}

interface ServiceDetailsAccordionProps {
  faqItems: FAQItem[];
}

export default function ServiceDetailsAccordion({ faqItems }: ServiceDetailsAccordionProps) {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleAccordion = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <div className="space-y-6">
      <h2 className="text-3xl font-bold text-white mb-8">
        Frequently Asked Questions
      </h2>
      
      <div className="space-y-4">
        {faqItems.map((item, index) => (
          <div 
            key={index} 
            className="bg-gray-900 rounded-2xl overflow-hidden transition-all duration-300"
          >
            {/* Question Header */}
            <button
              onClick={() => toggleAccordion(index)}
              className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-800 transition-colors"
            >
              <h3 className="text-lg font-semibold text-white pr-4">
                {item.question}
              </h3> //questionire question in postman
              <div className={`transform transition-transform duration-300 ${
                openIndex === index ? 'rotate-180' : ''
              }`}>
                <svg 
                  className="w-5 h-5 text-yellow-400" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M19 9l-7 7-7-7" 
                  />
                </svg>
              </div>
            </button>
            
            {/* Answer Content */}
            <div className={`overflow-hidden transition-all duration-300 ${
              openIndex === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
            }`}>
              <div className="px-6 pb-4">
                <p className="text-gray-300 leading-relaxed">
                  {item.answer}
                </p> //questionire answer in postman
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
