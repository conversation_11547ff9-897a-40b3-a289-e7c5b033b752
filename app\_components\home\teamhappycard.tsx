import Image from "next/image";
import aryan from "@/public/new-assests/news-icons/heroicons/homeimage/1.png";
import gulshan from "@/public/new-assests/news-icons/heroicons/homeimage/2.png";
import kunal from "@/public/new-assests/news-icons/heroicons/homeimage/3.png";

const TeamHappyClientsCard = () => {
  return (
    <div className="bg-gradient-to-br from-[#B5FF9D] to-[#8AFD60] rounded-3xl p-5 text-black flex flex-col justify-between aspect-[4/5] sm:aspect-[5/4] md:aspect-[4/3] lg:aspect-[5/4] w-full h-full shadow-lg transition-all duration-300 xl:order-2 lg:order-2 md:order-2 order-2 hover:shadow-xl hover:scale-[1.01]">
      {/* Team Members */}
      <div className="flex justify-center mb-4 sm:mb-6">
        <div className="flex items-center justify-center -space-x-3 sm:-space-x-4">
          {[aryan, gulshan, kunal].map((img, index) => (
            <div key={index} className="relative w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16">
              <Image
                src={img}
                alt={`Team member ${index + 1}`}
                fill
                className="rounded-full border-4 border-[#B5FF9D]/90 object-cover transition-transform duration-300 hover:scale-110 hover:z-10"
              />
            </div>
          ))}
          <div className="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 bg-[#65D83F] rounded-full flex items-center justify-center border-4 border-[#B5FF9D]/90 transition-transform duration-300 hover:scale-110">
            <span className="text-[#202020] font-bold text-xl sm:text-2xl">+</span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex flex-col items-center text-center gap-2 sm:gap-3">
        <p className="text-sm sm:text-base md:text-lg font-medium text-black/70">
          Happy Clients
        </p>
        <h3 className="text-4xl sm:text-5xl md:text-6xl font-extrabold tracking-tight">
          1000+
        </h3>
      </div>
    </div>
  );
};

export default TeamHappyClientsCard;