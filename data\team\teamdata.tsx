import gulshan from "@/public/new-assests/news-icons/heroicons/aboutus/gulshan.png";
import aryan from "@/public/new-assests/news-icons/heroicons/aboutus/aryan.png";
import kunal from "@/public/new-assests/news-icons/heroicons/aboutus/kunal.png";

export interface TeamMember {
  id: number;
  name: string;
  role: string;
  image: any;
  socialMedia: {
    facebook?: string;
    twitter?: string;
    linkedin?: string;
    instagram?: string;
  };
  isOwner: boolean;
}

export const teamData: TeamMember[] = [
  {
    id: 1,
    name: "<PERSON><PERSON>",
    role: "CEO & Founder",
    image: gulshan,
    socialMedia: {
      facebook: "https://facebook.com/jenilia",
      twitter: "https://twitter.com/jenilia",
      linkedin: "https://linkedin.com/in/jenilia"
    },
    isOwner: true
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON>",
    role: "CEO & Founder",
    image: aryan,
    socialMedia: {
      facebook: "https://facebook.com/asmaia",
      twitter: "https://twitter.com/asmaia",
      linkedin: "https://linkedin.com/in/asmaia"
    },
    isOwner: true
  },
  {
    id: 3,
    name: "<PERSON>ull<PERSON>sar",
    role:"CEO & Founder",
    image: kunal,
    socialMedia: {
      facebook: "https://facebook.com/azizullah",
      twitter: "https://twitter.com/azizullah",
      linkedin: "https://linkedin.com/in/azizullah"
    },
    isOwner: true
  },
   {
    id: 4,
    name: "Azizullah Basar",
    role:"CEO & Founder",
    image: gulshan,
    socialMedia: {
      facebook: "https://facebook.com/azizullah",
      twitter: "https://twitter.com/azizullah",
      linkedin: "https://linkedin.com/in/azizullah"
    },
    isOwner: true
  },
   {
    id: 5,
    name: "Azizullah Basar",
    role:"CEO & Founder",
    image: aryan,
    socialMedia: {
      facebook: "https://facebook.com/azizullah",
      twitter: "https://twitter.com/azizullah",
      linkedin: "https://linkedin.com/in/azizullah"
    },
    isOwner: true
  },
   {
    id: 6,
    name: "Azizullah Basar",
    role:"CEO & Founder",
    image: kunal,
    socialMedia: {
      facebook: "https://facebook.com/azizullah",
      twitter: "https://twitter.com/azizullah",
      linkedin: "https://linkedin.com/in/azizullah"
    },
    isOwner: true
  },
   {
    id: 7,
    name: "Azizullah Basar",
    role:"CEO & Founder",
    image: aryan,
    socialMedia: {
      facebook: "https://facebook.com/azizullah",
      twitter: "https://twitter.com/azizullah",
      linkedin: "https://linkedin.com/in/azizullah"
    },
    isOwner: true
  },
   {
    id: 8,
    name: "Azizullah Basar",
    role:"CEO & Founder",
    image: gulshan,
    socialMedia: {
      facebook: "https://facebook.com/azizullah",
      twitter: "https://twitter.com/azizullah",
      linkedin: "https://linkedin.com/in/azizullah"
    },
    isOwner: true
  },
   {
    id: 9,
    name: "Azizullah Basar",
    role:"CEO & Founder",
    image: kunal,
    socialMedia: {
      facebook: "https://facebook.com/azizullah",
      twitter: "https://twitter.com/azizullah",
      linkedin: "https://linkedin.com/in/azizullah"
    },
    isOwner: true
  }
];

// Filter owners only
export const ownersData = teamData.filter(member => member.isOwner);
