// src/api/contactApi.ts
import apiClient from "@/lib/apiclient";
// FAQ interface based on API response






export interface FAQ {
    id: number;
    question: string;
    answer: string;
    category: string | null;
    status: 'active' | 'inactive';
    order: number;
    createdAt: string;
    updatedAt: string;
}

// API Response interface
export interface FAQApiResponse {
    status: boolean;
    code: number;
    message: string;
    data: FAQ[];
    pagination?: {
        currentPage: number;
        totalPages: number;
        totalItems: number;
        itemsPerPage: number;
        hasNextPage: boolean;
        hasPrevPage: boolean;
    };
}

// Single FAQ Response interface
export interface SingleFAQResponse {
    status: boolean;
    code: number;
    message: string;
    data: FAQ;
}

// FAQ Statistics interface
export interface FAQStatistics {
    total: number;
    active: number;
    inactive: number;
    categories: Array<{ category: string; count: number }>;
}

// Create FAQ interface
export interface CreateFAQData {
    question: string;
    answer: string;
    category?: string;
    status?: 'active' | 'inactive';
    order?: number;
}

// Update FAQ interface
export interface UpdateFAQData {
    question?: string;
    answer?: string;
    category?: string;
    status?: 'active' | 'inactive';
    order?: number;
}

// Get all FAQs with optional filters
export const getAllFAQs = async (params?: {
    page?: number;
    limit?: number;
    category?: string;
    status?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}): Promise<FAQApiResponse> => {
    try {
        const queryParams = new URLSearchParams();
        
        if (params?.page) queryParams.append('page', params.page.toString());
        if (params?.limit) queryParams.append('limit', params.limit.toString());
        if (params?.category) queryParams.append('category', params.category);
        if (params?.status) queryParams.append('status', params.status);
        if (params?.search) queryParams.append('search', params.search);
        if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
        if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);

        const url = `/faqs${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const response = await apiClient.get(url);
        
        return response.data;
    } catch (error) {
        console.error('Error fetching FAQs:', error);
        throw error;
    }
};

// Get FAQ by ID
export const getFAQById = async (id: number): Promise<SingleFAQResponse> => {
    try {
        const response = await apiClient.get(`/faqs/${id}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching FAQ by ID:', error);
        throw error;
    }
};

// Create new FAQ
export const createFAQ = async (faqData: CreateFAQData): Promise<SingleFAQResponse> => {
    try {
        const response = await apiClient.post('/faqs', faqData);
        return response.data;
    } catch (error) {
        console.error('Error creating FAQ:', error);
        throw error;
    }
};

// Update FAQ by ID
export const updateFAQ = async (id: number, faqData: UpdateFAQData): Promise<SingleFAQResponse> => {
    try {
        const response = await apiClient.put(`/faqs/${id}`, faqData);
        return response.data;
    } catch (error) {
        console.error('Error updating FAQ:', error);
        throw error;
    }
};

// Delete FAQ by ID
export const deleteFAQ = async (id: number): Promise<{ status: boolean; message: string }> => {
    try {
        const response = await apiClient.delete(`/faqs/${id}`);
        return response.data;
    } catch (error) {
        console.error('Error deleting FAQ:', error);
        throw error;
    }
};

// Get FAQs by category
export const getFAQsByCategory = async (category: string): Promise<FAQApiResponse> => {
    try {
        const response = await apiClient.get(`/faqs/category/${category}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching FAQs by category:', error);
        throw error;
    }
};

// Get FAQ statistics
export const getFAQStatistics = async (): Promise<{ status: boolean; data: FAQStatistics }> => {
    try {
        const response = await apiClient.get('/faqs/statistics');
        return response.data;
    } catch (error) {
        console.error('Error fetching FAQ statistics:', error);
        throw error;
    }
};

// Get unique categories
export const getFAQCategories = async (): Promise<{ status: boolean; data: string[] }> => {
    try {
        const response = await getAllFAQs({ limit: 1000 }); // Get all FAQs to extract categories
        const categorySet = new Set<string>();
        response.data.forEach(faq => {
            if (faq.category && typeof faq.category === 'string') {
                categorySet.add(faq.category);
            }
        });
        const categories = Array.from(categorySet);
        return { status: true, data: categories };
    } catch (error) {
        console.error('Error fetching FAQ categories:', error);
        throw error;
    }
};

// Export default
export default {
    getAllFAQs,
    getFAQById,
    createFAQ,
    updateFAQ,
    deleteFAQ,
    getFAQsByCategory,
    getFAQStatistics,
    getFAQCategories
};
