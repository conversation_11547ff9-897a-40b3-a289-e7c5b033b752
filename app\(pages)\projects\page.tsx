

"use client";
import React from 'react';
import { ProjectCards } from './components/proejctcards';
import ContactUsHome from '@/app/_components/common/contactusHome';
import ScrollAnimationWrapper from "@/components/animations/ScrollAnimationWrapper";


export default function ProjectsPage() {
  return (
    <div className="transition-colors duration-300">
        <ScrollAnimationWrapper animation="fade-in-up">
          <ProjectCards/>
        </ScrollAnimationWrapper>

        {/* Contact section without animation as requested */}
        <ContactUsHome/>

    </div>
  );
}

