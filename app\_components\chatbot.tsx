// components/Chatbot.tsx
"use client";

import { useEffect } from "react";

export default function Chatbot() {
  useEffect(() => {
    // Chatbot config
    (window as any).cbbSettings = {
      base_url: "https://gmail547.chatbotbuilder.net",
      app_id: "gmail547-1",
      app_type: "chat",
    };
    // Dynamically load chatbot script
    const script = document.createElement("script");
    script.src = "https://gmail547.chatbotbuilder.net/embed.js";
    script.async = true;
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  return null; // This component doesn't render anything
}