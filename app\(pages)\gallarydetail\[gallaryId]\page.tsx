"use client";

import { useParams, useRouter } from "next/navigation";
import { useEffect } from "react";

const WorkDetail = () => {
  const { gallaryId } = useParams();
  const router = useRouter();

  useEffect(() => {
    // Redirect to the working project details route
    if (gallaryId) {
      // Since we don't have the project slug, we'll redirect to projects page
      // The user can then click on the specific project they want
      router.replace('/projects');
    }
  }, [gallaryId, router]);

  return (
    <div className="min-h-screen bg-black text-white flex items-center justify-center">
      <div className="text-center">
        <div className="text-[24px] font-bold">Redirecting to Projects...</div>
      </div>
    </div>
  );
};

export default WorkDetail;
